# DB-Performance

Swiss auto-tuning company website built with Next.js 14, Sanity v3, and Supabase.

## Tech Stack

- **Frontend**: Next.js 14 (App Router, TypeScript)
- **CMS**: Sanity v3 (Swiss region: ch-gva-1)
- **Database**: Supabase Postgres (eu-central-1)
- **Styling**: TailwindCSS + shadcn/ui
- **Testing**: Jest + <PERSON>wright
- **Deployment**: Vercel (fra1)

## Quick Start

1. **Install dependencies**
   ```bash
   pnpm install
   ```

2. **Set up environment**
   ```bash
   cp .env.local.example .env.local
   # Fill in your Supabase and Sanity credentials
   ```

3. **Run database migrations**
   ```bash
   # Set up Supabase locally or use hosted version
   supabase link --project-ref YOUR_PROJECT_REF
   supabase db push
   ```

4. **Seed database**
   ```bash
   pnpm seed
   ```

5. **Start development server**
   ```bash
   pnpm dev
   ```

## Database Schema

```sql
-- Vehicles table
create table vehicles (
  id text primary key,
  vehicle_type text not null,
  brand text not null,
  model text not null,
  year_range text not null,
  motor text not null,
  stages_count smallint default 0,
  inserted_at timestamptz default now()
);

-- Tuning stages table
create table stages (
  id text primary key,
  vehicle_id text references vehicles(id) on delete cascade,
  stage_name text not null,
  original_power text,
  tuned_power text,
  power_gain_ps text,
  power_gain_kw text,
  price_chf numeric,
  inserted_at timestamptz default now()
);
```

## Testing

```bash
# Unit tests
pnpm test

# Coverage report
pnpm test:coverage

# E2E tests
pnpm test:e2e

# All tests
pnpm test && pnpm test:e2e
```

## Deployment

### Vercel

1. Connect your repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push to main

### Environment Variables

Required for production:

```
SUPABASE_URL=
SUPABASE_ANON_KEY=
SUPABASE_SERVICE_KEY=
NEXT_PUBLIC_SANITY_PROJECT_ID=
NEXT_PUBLIC_SANITY_DATASET=production
SANITY_API_READ_TOKEN=
```

## Project Structure

```
src/
├── app/              # Next.js App Router
├── components/       # Reusable UI components
├── db/               # Supabase client & helpers
├── sanity/           # Sanity client & schemas
├── strings.ts        # UI copy (Swiss German)
└── styles/           # Global styles
```

## Company Information

- **Legal Name**: DB-Performance Garage Bytyci
- **Brand**: DB-Performance
- **Address**: Stauseestrasse 1, 5316 Leuggern, Schweiz
- **Website**: https://db-performance.ch

## License

Private - All rights reserved. 