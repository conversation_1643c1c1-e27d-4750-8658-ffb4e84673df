-- Enable RLS (Row Level Security) and UUID extension
create extension if not exists "uuid-ossp";

-- Create vehicles table
create table public.vehicles (
  id uuid default uuid_generate_v4() primary key,
  vehicle_type text not null,
  brand text not null,
  model text not null,
  year text,
  engine text not null,
  created_at timestamp with time zone default now() not null,
  updated_at timestamp with time zone default now() not null
);

-- Create stages table
create table public.stages (
  id uuid default uuid_generate_v4() primary key,
  vehicle_id uuid references public.vehicles(id) on delete cascade not null,
  name text not null,
  original_power integer,
  tuned_power integer,
  price_chf integer,
  description text,
  created_at timestamp with time zone default now() not null,
  updated_at timestamp with time zone default now() not null
);

-- Create indexes for better query performance
create index vehicles_vehicle_type_idx on public.vehicles(vehicle_type);
create index vehicles_brand_idx on public.vehicles(brand);
create index vehicles_model_idx on public.vehicles(model);
create index vehicles_year_idx on public.vehicles(year);
create index vehicles_engine_idx on public.vehicles(engine);
create index vehicles_compound_idx on public.vehicles(vehicle_type, brand, model, year, engine);

create index stages_vehicle_id_idx on public.stages(vehicle_id);
create index stages_name_idx on public.stages(name);
create index stages_original_power_idx on public.stages(original_power);
create index stages_tuned_power_idx on public.stages(tuned_power);
create index stages_price_idx on public.stages(price_chf);

-- Create RPC functions for filter options
create or replace function get_distinct_vehicle_types()
returns table(vehicle_type text)
language sql
stable
as $$
  select distinct v.vehicle_type
  from public.vehicles v
  where v.vehicle_type is not null
  order by v.vehicle_type;
$$;

create or replace function get_distinct_brands(p_vehicle_type text default null)
returns table(brand text)
language sql
stable
as $$
  select distinct v.brand
  from public.vehicles v
  where (p_vehicle_type is null or v.vehicle_type = p_vehicle_type)
    and v.brand is not null
  order by v.brand;
$$;

create or replace function get_distinct_years(p_vehicle_type text default null, p_brand text default null)
returns table(year text)
language sql
stable
as $$
  select distinct v.year
  from public.vehicles v
  where (p_vehicle_type is null or v.vehicle_type = p_vehicle_type)
    and (p_brand is null or v.brand = p_brand)
    and v.year is not null
  order by v.year;
$$;

create or replace function get_distinct_engines(
  p_vehicle_type text default null, 
  p_brand text default null,
  p_year text default null
)
returns table(engine text)
language sql
stable
as $$
  select distinct v.engine
  from public.vehicles v
  where (p_vehicle_type is null or v.vehicle_type = p_vehicle_type)
    and (p_brand is null or v.brand = p_brand)
    and (p_year is null or v.year = p_year)
    and v.engine is not null
  order by v.engine;
$$;

-- Create updated_at trigger function
create or replace function update_updated_at_column()
returns trigger
language plpgsql
as $$
begin
  new.updated_at = now();
  return new;
end;
$$;

-- Apply updated_at triggers
create trigger update_vehicles_updated_at
  before update on public.vehicles
  for each row
  execute function update_updated_at_column();

create trigger update_stages_updated_at
  before update on public.stages
  for each row
  execute function update_updated_at_column();

-- Enable RLS
alter table public.vehicles enable row level security;
alter table public.stages enable row level security;

-- Create policies (read-only for now)
create policy "Public read access for vehicles" on public.vehicles
  for select using (true);

create policy "Public read access for stages" on public.stages
  for select using (true);

-- Grant permissions
grant usage on schema public to anon, authenticated;
grant select on public.vehicles to anon, authenticated;
grant select on public.stages to anon, authenticated;
grant execute on function get_distinct_vehicle_types() to anon, authenticated;
grant execute on function get_distinct_brands(text) to anon, authenticated;
grant execute on function get_distinct_years(text, text) to anon, authenticated;
grant execute on function get_distinct_engines(text, text, text) to anon, authenticated; 