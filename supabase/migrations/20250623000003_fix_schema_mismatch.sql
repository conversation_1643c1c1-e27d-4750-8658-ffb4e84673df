-- Fix schema mismatch - rename engine to motor and update structure to match SMA data

-- Drop existing constraints and indexes
drop index if exists vehicles_engine_idx;
drop index if exists vehicles_compound_idx;

-- Alter vehicles table to match SMA data structure
alter table public.vehicles rename column engine to motor;

-- Add missing columns that were added in the second migration
alter table public.vehicles add column if not exists scraped_at timestamp with time zone;
alter table public.vehicles add column if not exists country text;
alter table public.vehicles add column if not exists total_stages integer;

-- Drop and recreate stages table with correct structure
drop table if exists public.stages cascade;

create table public.stages (
  id uuid default uuid_generate_v4() primary key,
  vehicle_id uuid references public.vehicles(id) on delete cascade not null,
  name text not null,
  stage_number integer,
  raw_table_text text,
  power_original text,
  power_tuned text,
  power_difference text,
  torque_original text,
  torque_tuned text,
  torque_difference text,
  price text,
  price_uvp text,
  created_at timestamp with time zone default now() not null,
  updated_at timestamp with time zone default now() not null
);

-- Recreate indexes with correct column names
create index vehicles_motor_idx on public.vehicles(motor);
create index vehicles_compound_idx on public.vehicles(vehicle_type, brand, model, year, motor);
create index stages_vehicle_id_idx on public.stages(vehicle_id);
create index stages_name_idx on public.stages(name);
create index stages_stage_number_idx on public.stages(stage_number);

-- Update RPC functions to use motor instead of engine
drop function if exists get_distinct_engines(text, text, text);

create or replace function get_distinct_motors(
  p_vehicle_type text default null, 
  p_brand text default null,
  p_model text default null,
  p_year text default null
)
returns table(motor text)
language sql
stable
as $$
  select distinct v.motor
  from public.vehicles v
  where (p_vehicle_type is null or v.vehicle_type = p_vehicle_type)
    and (p_brand is null or v.brand = p_brand)
    and (p_model is null or v.model = p_model)
    and (p_year is null or v.year = p_year)
    and v.motor is not null
  order by v.motor;
$$;

create or replace function get_distinct_models(
  p_vehicle_type text default null, 
  p_brand text default null
)
returns table(model text)
language sql
stable
as $$
  select distinct v.model
  from public.vehicles v
  where (p_vehicle_type is null or v.vehicle_type = p_vehicle_type)
    and (p_brand is null or v.brand = p_brand)
    and v.model is not null
  order by v.model;
$$;

-- Apply updated_at trigger to new stages table
create trigger update_stages_updated_at
  before update on public.stages
  for each row
  execute function update_updated_at_column();

-- Update RLS policies
alter table public.stages enable row level security;

create policy "Public read access for stages" on public.stages
  for select using (true);

-- Create the get_vehicles_with_stages function with correct schema
create or replace function get_vehicles_with_stages(
  p_vehicle_type text default null,
  p_brand text default null,
  p_model text default null,
  p_year text default null,
  p_motor text default null,
  p_limit integer default null
)
returns table(
  id uuid,
  vehicle_type text,
  brand text,
  model text,
  year text,
  motor text,
  scraped_at timestamp with time zone,
  country text,
  total_stages integer,
  stages jsonb
)
language sql
stable
as $$
  select 
    v.id::uuid,
    v.vehicle_type::text,
    v.brand::text,
    v.model::text,
    v.year::text,
    v.motor::text,
    v.scraped_at::timestamp with time zone,
    v.country::text,
    v.total_stages::integer,
    coalesce(
      jsonb_agg(
        jsonb_build_object(
          'name', s.name,
          'stage_number', s.stage_number,
          'raw_table_text', s.raw_table_text,
          'power_original', s.power_original,
          'power_tuned', s.power_tuned,
          'power_difference', s.power_difference,
          'torque_original', s.torque_original,
          'torque_tuned', s.torque_tuned,
          'torque_difference', s.torque_difference,
          'price', s.price,
          'price_uvp', s.price_uvp
        ) order by s.stage_number
      ) filter (where s.id is not null),
      '[]'::jsonb
    )::jsonb as stages
  from public.vehicles v
  left join public.stages s on v.id = s.vehicle_id
  where (p_vehicle_type is null or v.vehicle_type = p_vehicle_type)
    and (p_brand is null or v.brand = p_brand)
    and (p_model is null or v.model = p_model)
    and (p_year is null or v.year = p_year)
    and (p_motor is null or v.motor = p_motor)
  group by v.id, v.vehicle_type, v.brand, v.model, v.year, v.motor, v.scraped_at, v.country, v.total_stages
  order by v.brand, v.model, v.year, v.motor
  limit p_limit;
$$;

-- Grant permissions
grant select on public.stages to anon, authenticated;
grant execute on function get_distinct_motors(text, text, text, text) to anon, authenticated;
grant execute on function get_distinct_models(text, text) to anon, authenticated;
grant execute on function get_vehicles_with_stages(text, text, text, text, text, integer) to anon, authenticated; 