-- SQL function to search vehicles without 1000 row limit
-- This should be created in your Supabase SQL Editor

CREATE OR REPLACE FUNCTION search_vehicles(
  search_term TEXT DEFAULT NULL,
  vehicle_type_filter TEXT DEFAULT NULL,
  brand_filter TEXT DEFAULT NULL,
  limit_count INTEGER DEFAULT 20,
  offset_count INTEGER DEFAULT 0
)
RETURNS TABLE (
  id TEXT,
  vehicle_type TEXT,
  brand TEXT,
  model TEXT,
  year TEXT,
  engine TEXT,
  motor TEXT,
  created_at TIMESTAMPTZ
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    v.id,
    v.vehicle_type,
    v.brand,
    v.model,
    v.year,
    v.engine,
    v.motor,
    v.created_at
  FROM vehicles v
  WHERE 
    (search_term IS NULL OR (
      v.brand ILIKE '%' || search_term || '%' OR
      v.model ILIKE '%' || search_term || '%' OR
      v.engine ILIKE '%' || search_term || '%' OR
      v.motor ILIKE '%' || search_term || '%'
    ))
    AND (vehicle_type_filter IS NULL OR v.vehicle_type = vehicle_type_filter)
    AND (brand_filter IS NULL OR v.brand = brand_filter)
  ORDER BY v.brand ASC, v.model ASC
  LIMIT limit_count
  OFFSET offset_count;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION search_vehicles TO authenticated;
GRANT EXECUTE ON FUNCTION search_vehicles TO anon;

-- Alternative: Create a more advanced full-text search function
CREATE OR REPLACE FUNCTION search_vehicles_fulltext(
  search_term TEXT DEFAULT NULL,
  vehicle_type_filter TEXT DEFAULT NULL,
  brand_filter TEXT DEFAULT NULL,
  limit_count INTEGER DEFAULT 20,
  offset_count INTEGER DEFAULT 0
)
RETURNS TABLE (
  id TEXT,
  vehicle_type TEXT,
  brand TEXT,
  model TEXT,
  year TEXT,
  engine TEXT,
  motor TEXT,
  created_at TIMESTAMPTZ,
  search_rank REAL
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    v.id,
    v.vehicle_type,
    v.brand,
    v.model,
    v.year,
    v.engine,
    v.motor,
    v.created_at,
    CASE 
      WHEN search_term IS NULL THEN 1.0
      ELSE (
        CASE WHEN v.brand ILIKE '%' || search_term || '%' THEN 0.4 ELSE 0.0 END +
        CASE WHEN v.model ILIKE '%' || search_term || '%' THEN 0.3 ELSE 0.0 END +
        CASE WHEN v.engine ILIKE '%' || search_term || '%' THEN 0.2 ELSE 0.0 END +
        CASE WHEN v.motor ILIKE '%' || search_term || '%' THEN 0.1 ELSE 0.0 END
      )
    END as search_rank
  FROM vehicles v
  WHERE 
    (search_term IS NULL OR (
      v.brand ILIKE '%' || search_term || '%' OR
      v.model ILIKE '%' || search_term || '%' OR
      v.engine ILIKE '%' || search_term || '%' OR
      v.motor ILIKE '%' || search_term || '%'
    ))
    AND (vehicle_type_filter IS NULL OR v.vehicle_type = vehicle_type_filter)
    AND (brand_filter IS NULL OR v.brand = brand_filter)
  ORDER BY 
    CASE WHEN search_term IS NULL THEN v.brand ELSE search_rank END DESC,
    v.brand ASC, 
    v.model ASC
  LIMIT limit_count
  OFFSET offset_count;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION search_vehicles_fulltext TO authenticated;
GRANT EXECUTE ON FUNCTION search_vehicles_fulltext TO anon;
