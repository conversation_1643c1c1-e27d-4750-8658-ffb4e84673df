const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
})

/** @type {import('next').NextConfig} */
const nextConfig = {
  // Improved build safety - only skip in development or with explicit flag
  eslint: {
    // Allow builds but warn about issues
    ignoreDuringBuilds: process.env.NODE_ENV === 'development' || process.env.SKIP_LINT === 'true',
  },
  typescript: {
    // More strict but allow emergency builds
    ignoreBuildErrors: process.env.SKIP_TYPECHECK === 'true',
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'cdn.sanity.io',
      },
    ],
  },
  experimental: {
    typedRoutes: true,
  },
  async redirects() {
    return [
      {
        source: '/admin',
        destination: '/studio',
        permanent: true,
      },
    ];
  },
}

module.exports = withBundleAnalyzer(nextConfig) 