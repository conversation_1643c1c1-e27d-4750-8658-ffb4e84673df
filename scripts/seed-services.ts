import { createClient } from 'next-sanity';

// Load environment variables
import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

// Direct client configuration
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID!,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET!,
  apiVersion: process.env.NEXT_PUBLIC_SANITY_API_VERSION || '2024-01-01',
  useCdn: false,
  token: process.env.SANITY_API_READ_TOKEN,
});

const servicesPageData = {
  _type: 'servicesPage',
  heroTitle: 'Unsere Dienstleistungen',
  heroSubtitle: 'Professionelle Fahrzeugoptimierung auf höchstem Niveau',
  heroDescription: 'Von der Kennfeldoptimierung bis zur kompletten Leistungssteigerung - wir bieten individuelle Lösungen für Ihr Fahrzeug.',
  services: [
    {
      _type: 'serviceCard',
      _key: 'chiptuning',
      title: 'Chiptuning',
      description: 'Professionelle Kennfeldoptimierung für mehr Leistung, Drehmoment und besseren Kraftstoffverbrauch.',
      features: ['Bis zu 30% mehr Leistung', 'Optimierter Kraftstoffverbrauch', 'Erhaltung der Garantie'],
      buttonText: 'Konfigurator starten',
      buttonLink: '/tuning',
      isExternalLink: false,
      color: 'blue',
      icon: 'lightning',
      order: 1,
    },
    {
      _type: 'serviceCard',
      _key: 'leistungsmessung',
      title: 'Leistungsmessung',
      description: 'Präzise Messung auf unserem Allrad-Leistungsprüfstand für exakte Ergebnisse vor und nach dem Tuning.',
      features: ['Allrad-Prüfstand', 'Vorher/Nachher Messung', 'Detaillierter Bericht'],
      buttonText: 'Termin vereinbaren',
      buttonLink: '#contact',
      isExternalLink: false,
      color: 'green',
      icon: 'chart',
      order: 2,
    },
    {
      _type: 'serviceCard',
      _key: 'scheiben-versicherung',
      title: 'Scheiben ersetzen über Versicherung',
      description: 'Wir machen alles für dich - komplette Abwicklung über die Versicherung mit Ersatzwagen falls gewünscht.',
      features: ['Komplette Versicherungsabwicklung', 'Ersatzwagen verfügbar', 'Professionelle Montage'],
      buttonText: 'Beratung anfragen',
      buttonLink: '#contact',
      isExternalLink: false,
      color: 'orange',
      icon: 'cog',
      order: 3,
    },
    {
      _type: 'serviceCard',
      _key: 'motorrad-tuning',
      title: 'Motorrad Tuning',
      description: 'Spezielle Optimierungen für Motorräder, Quads und andere Zweiräder für maximale Performance.',
      features: ['Motorrad-Spezialisten', 'Quick-Shifter Anpassung', 'Drehzahlbegrenzer-off'],
      buttonText: 'Modelle anzeigen',
      buttonLink: '/tuning',
      isExternalLink: false,
      color: 'purple',
      icon: 'clock',
      order: 4,
    },
    {
      _type: 'serviceCard',
      _key: 'sonderfahrzeuge',
      title: 'Sonderfahrzeuge',
      description: 'Tuning für Boote, Jet-Skis, Traktoren und andere Spezialfahrzeuge mit individuellen Lösungen.',
      features: ['Marine-Motoren', 'Landmaschinen', 'Spezialanfertigungen'],
      buttonText: 'Verfügbarkeit prüfen',
      buttonLink: '/tuning',
      isExternalLink: false,
      color: 'red',
      icon: 'beaker',
      order: 5,
    },
    {
      _type: 'serviceCard',
      _key: 'beratung-service',
      title: 'Beratung & Service',
      description: 'Umfassende Beratung, After-Sales-Service und Support für alle unsere Tuning-Lösungen.',
      features: ['Kostenlose Erstberatung', '24/7 Support', 'Lebenslange Garantie'],
      buttonText: 'Kontakt aufnehmen',
      buttonLink: '#contact',
      isExternalLink: false,
      color: 'teal',
      icon: 'chat',
      order: 6,
    },
  ],
  ctaTitle: 'Bereit für mehr Leistung?',
  ctaDescription: 'Entdecken Sie das Potenzial Ihres Fahrzeugs mit unserem Tuning-Konfigurator',
  ctaButtonText: 'Konfigurator starten',
};

async function seedServicesPage() {
  try {
    console.log('🌱 Seeding services page data...');
    
    // Delete existing services page to recreate with proper _key values
    const existing = await client.fetch('*[_type == "servicesPage"][0]');
    
    if (existing) {
      console.log('🗑️ Deleting existing services page...');
      await client.delete(existing._id);
      console.log('✅ Existing services page deleted');
    }
    
    console.log('📝 Creating new services page with proper _key values...');
    const result = await client.create(servicesPageData);
    console.log('✅ Services page created:', result._id);
    
    console.log('🎉 Services page seeding completed!');
  } catch (error) {
    console.error('❌ Error seeding services page:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  seedServicesPage();
}

export { seedServicesPage }; 