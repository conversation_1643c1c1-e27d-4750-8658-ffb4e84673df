import { createClient } from '@sanity/client'

const projectId = 'uzktk1gg'
// Using the admin token from CLI debug
const adminToken = 'skkr72eqIhavsdBJHo4bW4tgRcgD0PJQ0LYieeR5ScPe3yiOPE5jCDXFke9w09FdAVItL43v1Z14CsSoq'
const datasets = ['development', 'production']
const newPhoneNumber = '+41 76 250 07 61'

async function updatePhoneNumber() {
  for (const dataset of datasets) {
    console.log(`\n🔄 Updating phone number in ${dataset} dataset...`)

    const client = createClient({
      projectId,
      dataset,
      token: adminToken,
      useCdn: false,
      apiVersion: '2024-01-01'
    })

    try {
      // Find the site settings document
      const siteSettings = await client.fetch('*[_type == "siteSettings"][0]')
      
      if (!siteSettings) {
        console.log(`❌ No site settings found in ${dataset} dataset`)
        continue
      }

      console.log(`📋 Current phone number in ${dataset}:`, siteSettings.footer?.phone || 'Not set')

      // Update the phone number
      const result = await client
        .patch(siteSettings._id)
        .set({
          'footer.phone': newPhoneNumber
        })
        .commit()

      console.log(`✅ Successfully updated phone number in ${dataset} dataset to: ${newPhoneNumber}`)
      console.log(`📄 Document ID: ${result._id}`)
      
    } catch (error) {
      console.error(`❌ Error updating ${dataset} dataset:`, error.message)
    }
  }
  
  console.log('\n🎉 Phone number update completed!')
}

updatePhoneNumber().catch(console.error)
