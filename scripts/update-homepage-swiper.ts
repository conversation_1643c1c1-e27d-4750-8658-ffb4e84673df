import { createClient } from '@sanity/client'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config({ path: '.env.local' })

const projectId = process.env.NEXT_PUBLIC_SANITY_PROJECT_ID
const token = process.env.SANITY_API_READ_TOKEN

if (!projectId || !token) {
  console.error('Missing required environment variables')
  process.exit(1)
}

// Create clients for both datasets
const developmentClient = createClient({
  projectId,
  dataset: 'development',
  useCdn: false,
  token,
  apiVersion: '2024-01-01',
})

const productionClient = createClient({
  projectId,
  dataset: 'production',
  useCdn: false,
  token,
  apiVersion: '2024-01-01',
})

const swipingServicesData = [
  {
    _type: 'swipingService',
    _key: 'tuning',
    text: 'Fahrzeugoptimierung',
    order: 1,
  },
  {
    _type: 'swipingService',
    _key: 'werkstatt',
    text: 'Werkstatt-Services',
    order: 2,
  },
  {
    _type: 'swipingService',
    _key: 'aufbereitung',
    text: 'Fahrzeugaufbereitung',
    order: 3,
  },
  {
    _type: 'swipingService',
    _key: 'tuning-alt',
    text: 'Chiptuning',
    order: 4,
  },
]

async function updateHomepageData(client: any, datasetName: string) {
  try {
    console.log(`Updating homepage data in ${datasetName} dataset...`)
    
    // Get existing homepage document
    const existingHomepage = await client.fetch('*[_type == "homepage"][0]')
    
    if (!existingHomepage) {
      console.log(`No homepage document found in ${datasetName}. Creating new one...`)
      
      const newHomepage = {
        _type: 'homepage',
        heroTitle: 'Professionelle Fahrzeugoptimierung auf höchstem Niveau',
        heroSubtitle: 'Maximieren Sie die Leistung Ihres Fahrzeugs mit unserem Chiptuning-Service',
        heroDescription: 'Entdecken Sie das versteckte Potenzial Ihres Motors durch professionelle Software-Optimierung. Mehr Leistung, besserer Kraftstoffverbrauch und optimiertes Fahrverhalten.',
        ctaButtonText: 'Konfigurator starten',
        ctaSecondaryText: 'Unsere Services',
        swipingServices: swipingServicesData,
        features: [
          {
            _type: 'featureCard',
            _key: 'performance',
            title: 'Leistungssteigerung',
            description: 'Bis zu 30% mehr Leistung durch professionelle Software-Optimierung',
            icon: 'lightning',
            order: 1,
          },
          {
            _type: 'featureCard',
            _key: 'garage',
            title: 'Garagen-Service',
            description: 'Komplette Werkstattdienstleistungen: Ölservice, Bremsen, Getriebe und allgemeine Reparaturen',
            icon: 'cog',
            order: 2,
          },
          {
            _type: 'featureCard',
            _key: 'speed',
            title: 'Schneller Service',
            description: 'Terminvereinbarung innerhalb von 24 Stunden möglich',
            icon: 'clock',
            order: 3,
          },
        ],
      }
      
      const result = await client.create(newHomepage)
      console.log(`✅ Created homepage document in ${datasetName}:`, result._id)
    } else {
      console.log(`Found existing homepage document in ${datasetName}: ${existingHomepage._id}`)
      
      // Update existing document with swipingServices
      const result = await client
        .patch(existingHomepage._id)
        .set({ swipingServices: swipingServicesData })
        .commit()
      
      console.log(`✅ Updated homepage document in ${datasetName}:`, result._id)
    }
  } catch (error) {
    console.error(`❌ Error updating homepage data in ${datasetName}:`, error)
  }
}

async function main() {
  console.log('🚀 Starting homepage swiper data update...')
  
  await updateHomepageData(developmentClient, 'development')
  await updateHomepageData(productionClient, 'production')
  
  console.log('✅ Homepage swiper data update completed!')
}

main().catch(console.error)
