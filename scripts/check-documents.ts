import { createClient } from 'next-sanity';

// Load environment variables
import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID!,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET!,
  apiVersion: process.env.NEXT_PUBLIC_SANITY_API_VERSION || '2024-01-01',
  useCdn: false,
  token: process.env.SANITY_API_READ_TOKEN,
});

async function checkDocuments() {
  try {
    const dataset = process.env.NEXT_PUBLIC_SANITY_DATASET;
    const projectId = process.env.NEXT_PUBLIC_SANITY_PROJECT_ID;
    
    console.log(`🔍 Checking documents in "${dataset}" dataset for project "${projectId}"...`);
    
    const docs = await client.fetch('*[_type in ["homepage", "tuningPage", "siteSettings", "servicesPage"]]{ _type, _id }');
    
    console.log('Found documents:', docs);
    console.log(`Total: ${docs.length} documents`);
  } catch (error) {
    console.error('❌ Error checking documents:', error);
  }
}

checkDocuments(); 