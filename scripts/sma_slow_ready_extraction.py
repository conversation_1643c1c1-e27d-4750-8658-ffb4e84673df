#!/usr/bin/env python3
"""
SMA SLOW-READY Extraction - Adaptive to Website Speed
⏳ Extended waits for slower SMA website
🔄 Resume capability maintained
🎯 Based on working balanced scraper
🛡️ Reliable data extraction
"""

import json
import time
import random
import string
import hashlib
import os
import glob
import re
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager
from seleniumwire import webdriver as wire_webdriver

class SMASlowReadyExtractor:
    def __init__(self, use_proxy=True):
        # --- Proxy handling -------------------------------------------------
        # The scraper MUST always use an IPRoyal proxy. We keep a list of
        # >900 pre-generated credentials (host, port, user, pass) in
        #   scripts/iproyal-proxies.csv
        # CSV format: Host,Port,User,Pass (*Pass may itself contain commas*)
        # We load the file once and iterate through the proxies on failure.

        self.proxy_csv_path: str = os.path.join(os.path.dirname(__file__), "iproyal-proxies.csv")
        self.proxy_list: list[str] = self._load_proxy_list()
        self.proxy_index: int = 0  # current proxy pointer
        self.use_proxy = True  # always true by project requirement
        
        # Smart retry system with progressive waits
        self.retry_config = {
            'max_retries': 5,      # Max retry attempts
            'base_wait': 1.0,      # Start with 1s
            'max_wait': 10.0,      # Max 10s wait
            'progressive_factor': 2.0,  # Double wait time each retry
            'current_wait': 1.0
        }
        
        # Simple proxy rotation - no country switching needed
        self.proxy_rotation_count = 0
        self.vehicles_per_proxy_switch = 100  # Switch proxy every 100 vehicles
        
        # Files - FIXED paths
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        self.progress_file = "sma_extraction_progress.json"
        self.results_file = f"complete_extraction_selenium/sma_intelligent_{timestamp}.json"
        self.vehicle_structure_file = "vehicle_structure.json"
        
        # Progress tracking
        self.progress = {
            'completed_combinations': set(),
            'current_position': {},
            'total_estimated': 0
        }
        
        self.stats = {
            'vehicles_scraped': 0,
            'stages_extracted': 0,
            'errors': 0,
            'start_time': datetime.now()
        }
        
        self.extracted_data = []
        self.resume_combo_id: str | None = None
        self.load_progress()
        
    def generate_session_id(self):
        """Generate unique session ID for proxy rotation"""
        chars = string.ascii_letters + string.digits
        return ''.join(random.choice(chars) for _ in range(8))
        
    def create_combination_id(self, vehicle_type, brand, model, year, motor):
        """Create unique ID for vehicle combination"""
        combo = f"{vehicle_type}|{brand}|{model}|{year}|{motor}"
        return hashlib.md5(combo.encode()).hexdigest()
        
    def load_progress(self):
        """Load previous extraction progress"""
        try:
            if os.path.exists(self.progress_file):
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.progress['completed_combinations'] = set(data.get('completed_combinations', []))
                    self.progress['current_position'] = data.get('current_position', {})
                    self.progress['total_estimated'] = data.get('total_estimated', 0)
                    
                    if 'stats' in data:
                        self.stats.update(data['stats'])
                        self.stats['start_time'] = datetime.now()  # Reset start time
                        
                print(f"📂 Resume: Loaded {len(self.progress['completed_combinations'])} completed combinations")
            else:
                print("📂 No previous progress found - starting fresh")
                
            # For fresh extraction, do not load any existing combinations
            # Only load combinations if we want to resume (which we don't for fresh start)
            # self._load_current_session_combinations()
            
        except Exception as e:
            print(f"⚠️  Could not load progress: {str(e)}")
            
    def _load_current_session_combinations(self):
        """Load combinations only from current session results file for proper resume"""
        if hasattr(self, 'results_file') and os.path.exists(self.results_file):
            try:
                with open(self.results_file, 'r', encoding='utf-8') as f:
                    existing_data = json.load(f)
                    
                loaded_count = 0
                for vehicle in existing_data:
                    combination_id = self.create_combination_id(
                        vehicle['vehicle_type'],
                        vehicle['brand'],
                        vehicle['model'],
                        vehicle['year'],
                        vehicle['motor']
                    )
                    self.progress['completed_combinations'].add(combination_id)
                    loaded_count += 1
                    
                if loaded_count > 0:
                    print(f"📂 Loaded {loaded_count} current session combinations for resume")
                    
            except Exception as e:
                print(f"⚠️  Could not load current session data: {str(e)}")
        else:
            print("📂 No current session file found - starting fresh extraction")
            
        # Don't load any old/previous data files - only current session for true resume

    def find_resume_position(self):
        """Find the best position to resume extraction based on completed combinations"""
        cp = self.progress.get('current_position')
        if cp and cp.get('vehicle_type'):
            # Resume position can be at vehicle type level (brand empty) or brand level
            brand_info = f" → {cp['brand']}" if cp.get('brand') else " (start of vehicle type)"
            print(f"🎯 Resume position: {cp['vehicle_type']}{brand_info}")
            return {
                'vehicle_type': cp['vehicle_type'],
                'brand': cp.get('brand', ''),
                'resume_after': True
            }

        return None
            
    def should_rotate_proxy(self):
        """Check if we should rotate to next proxy"""
        return self.proxy_rotation_count >= self.vehicles_per_proxy_switch
        
    def rotate_proxy(self):
        """Rotate to next proxy and restart browser with proper cleanup"""
        if not self.use_proxy:
            return True
            
        self.proxy_rotation_count = 0
        
        print(f"🔄 Proxy rotation after {self.vehicles_per_proxy_switch} vehicles")
        
        # Proper browser cleanup
        if hasattr(self, 'driver') and self.driver:
            try:
                self.driver.quit()
            except Exception as e:
                print(f"⚠️  Error during browser cleanup: {str(e)}")
            finally:
                self.driver = None
                time.sleep(2)  # Give system time to release resources
        
        # Setup new browser with next proxy and reconnect to configurator
        if not self.setup_browser():
            print("❌ Failed to setup new browser after proxy rotation")
            return False
            
        if not self.navigate_to_configurator():
            print("❌ Failed to reconnect to configurator after proxy rotation")
            return False
            
        print("✅ Proxy rotation successful - ready to continue extraction")
        return True
        
    def intelligent_retry(self, operation, description="operation", **kwargs):
        """Intelligent retry with progressive wait times and page refresh on loading failures"""
        wait_time = self.retry_config['base_wait']
        
        for attempt in range(self.retry_config['max_retries']):
            try:
                result = operation(**kwargs)
                if result:  # Success
                    self.retry_config['current_wait'] = self.retry_config['base_wait']  # Reset wait time
                    return result
                else:
                    raise Exception("Operation returned False/None")
                    
            except Exception as e:
                if attempt < self.retry_config['max_retries'] - 1:  # Not last attempt
                    print(f"⚠️  {description} failed (attempt {attempt + 1}), retrying in {wait_time:.1f}s...")
                    print(f"    Error: {str(e)}")
                    
                    # After 2 failed attempts on loading operations, try refreshing the page
                    if attempt >= 1 and "loading" in description.lower():
                        try:
                            print("    🔄 Refreshing page to fix loading issues...")
                            self.driver.refresh()
                            time.sleep(3)  # Wait for page to reload
                            
                            # Re-navigate to the configurator if needed
                            if "models" in description.lower() or "years" in description.lower() or "motors" in description.lower():
                                # Restore basic page state after refresh
                                WebDriverWait(self.driver, 30).until(
                                    EC.presence_of_element_located((By.ID, "vehicle_types"))
                                )
                                time.sleep(2)
                                print("    ✅ Page refreshed and basic state restored")
                        except Exception as refresh_error:
                            print(f"    ⚠️  Page refresh failed: {str(refresh_error)[:50]}")
                    
                    time.sleep(wait_time)
                    
                    # Progressive wait increase
                    wait_time = min(
                        self.retry_config['max_wait'],
                        wait_time * self.retry_config['progressive_factor']
                    )
                else:
                    print(f"❌ {description} failed after {self.retry_config['max_retries']} attempts")
                    return False
        
        return False
    
    def save_progress(self):
        """Save current extraction progress - FIXED datetime serialization"""
        try:
            progress_to_save = {
                'completed_combinations': list(self.progress['completed_combinations']),
                'current_position': self.progress['current_position'],
                'total_estimated': self.progress['total_estimated'],
                'last_save': datetime.now().isoformat(),
                'stats': {
                    'vehicles_scraped': self.stats['vehicles_scraped'],
                    'stages_extracted': self.stats['stages_extracted'],
                    'errors': self.stats['errors'],
                    'start_time': self.stats['start_time'].isoformat()
                }
            }
            
            with open(self.progress_file, 'w', encoding='utf-8') as f:
                json.dump(progress_to_save, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"⚠️  Could not save progress: {str(e)}")
            
    def save_results(self):
        """Save extracted results"""
        try:
            if self.extracted_data:
                with open(self.results_file, 'w', encoding='utf-8') as f:
                    json.dump(self.extracted_data, f, ensure_ascii=False, indent=2)
                print(f"💾 Results saved: {self.results_file}")
        except Exception as e:
            print(f"⚠️  Could not save results: {str(e)}")
            
    def setup_browser(self):
        """Setup Chrome browser with selenium-wire proxy support. Automatically falls back to direct connection if the proxy tunnel fails."""
        # Always try proxies; iterate until a working tunnel is found or all exhausted
        max_attempts = len(self.proxy_list) if self.use_proxy else 1
        attempt = 0
        while attempt < max_attempts:
            attempt += 1
            proxy_url = self._get_next_proxy() if self.use_proxy else None
            attempt_desc = f"proxy {attempt}/{max_attempts}" if proxy_url else "DIRECT"
            
            mode = f"IPRoyal ({attempt}/{max_attempts})" if proxy_url else "DIRECT"
            print(f"🌐 Setting up INTELLIGENT browser via {mode} …")

            # Chrome Options – SLOW-READY
            chrome_options = Options()
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')

            # Selenium-Wire proxy options (only when proxy is active)
            seleniumwire_options = {}
            if proxy_url:
                seleniumwire_options = {
                    'proxy': {
                        'http': proxy_url,
                        'https': proxy_url,
                        'no_proxy': 'localhost,127.0.0.1'
                    }
                }
                print(f"🔗 Using proxy: {proxy_url}")

            # Setup service and driver
            service = Service(ChromeDriverManager().install())
            self.driver = wire_webdriver.Chrome(
                service=service,
                options=chrome_options,
                seleniumwire_options=seleniumwire_options
            )

            # Stealth: hide webdriver property
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            # Quick connectivity check (with/without proxy)
            try:
                self.driver.get("https://ipv4.icanhazip.com")
                ip_text = self.driver.find_element(By.TAG_NAME, "body").text.strip()
                print(f"✅ Connectivity OK – external IP: {ip_text}")
            except Exception as conn_exc:
                print(f"⚠️  Proxy connectivity failed: {str(conn_exc)[:120]}…")
                # Clean up driver and try next proxy
                try:
                    self.driver.quit()
                except Exception:
                    pass
                self.driver = None
                continue  # try next proxy

            print("✅ Browser setup successful")
            return True  # success!

        # Should not reach here but safeguard
        return False
            
    def navigate_to_configurator(self):
        """Navigate to SMA configurator with INTELLIGENT retry"""
        def _try_load_configurator():
            self.driver.get("https://www.sma-tuning.de/fahrzeug-konfigurator.html")
            
            # Wait for basic page structure
            WebDriverWait(self.driver, 30).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            time.sleep(2)  # Basic wait for JavaScript
            
            # Wait for vehicle_types dropdown
            WebDriverWait(self.driver, 30).until(
                EC.presence_of_element_located((By.ID, "vehicle_types"))
            )
            
            time.sleep(1)  # Wait for dropdown population
            
            # Verify dropdown is populated
            vehicle_types_select = Select(self.driver.find_element(By.ID, "vehicle_types"))
            options = vehicle_types_select.options
            
            if len(options) <= 1:
                raise Exception("Dropdown not populated")
                
            return True
        
        print("🔧 Loading configurator (INTELLIGENT mode)...")
        result = self.intelligent_retry(_try_load_configurator, "Configurator loading")
        
        if result:
            print("✅ Configurator fully loaded")
        
        return result
    
    def scan_and_save_vehicle_structure(self):
        """Scan all vehicle categories and brands, save to file for reliable resume"""
        structure_file = self.vehicle_structure_file
        
        # Check if structure file already exists and is recent (less than 7 days old)
        if os.path.exists(structure_file):
            file_age = time.time() - os.path.getmtime(structure_file)
            if file_age < 7 * 24 * 60 * 60:  # 7 days
                print(f"📋 Using existing vehicle structure from {structure_file}")
                return self.load_vehicle_structure()
        
        print("🔍 Scanning all vehicle categories and brands...")
        
        vehicle_structure = {}
        
        # Get all vehicle types
        vehicle_types_select = Select(self.driver.find_element(By.ID, "vehicle_types"))
        raw_options = vehicle_types_select.options
        vehicle_type_options = [opt for opt in raw_options if opt.get_attribute('value').strip() and not re.search(r'bitte', opt.text, re.I)]
        
        for vehicle_type_option in vehicle_type_options:
            vehicle_type_value = vehicle_type_option.get_attribute('value')
            vehicle_type_text = vehicle_type_option.text.strip()
            
            if not vehicle_type_value or not vehicle_type_text:
                continue
                
            print(f"  📋 Scanning: {vehicle_type_text}")
            
            # Select vehicle type
            vehicle_types_select.select_by_value(vehicle_type_value)
            time.sleep(2)
            
            # Wait for brands to load
            try:
                WebDriverWait(self.driver, 10).until(
                    lambda driver: len(Select(driver.find_element(By.ID, "vehicle_brands")).options) > 1
                )
                
                # Get all brands for this vehicle type
                brands_select = Select(self.driver.find_element(By.ID, "vehicle_brands"))
                brand_options = brands_select.options[1:]  # Skip "Bitte wählen"
                
                brands_list = []
                for brand_option in brand_options:
                    brand_value = brand_option.get_attribute('value')
                    brand_text = brand_option.text.strip()
                    
                    if brand_value and brand_text:
                        brands_list.append({
                            'value': brand_value,
                            'text': brand_text
                        })
                
                vehicle_structure[vehicle_type_text] = {
                    'value': vehicle_type_value,
                    'brands': brands_list
                }
                
                print(f"    ✅ Found {len(brands_list)} brands")
                
            except Exception as e:
                print(f"    ❌ Error scanning {vehicle_type_text}: {str(e)}")
                continue
        
        # Save structure to file
        try:
            with open(structure_file, 'w', encoding='utf-8') as f:
                json.dump(vehicle_structure, f, ensure_ascii=False, indent=2)
            print(f"💾 Vehicle structure saved to {structure_file}")
        except Exception as e:
            print(f"⚠️ Could not save vehicle structure: {str(e)}")
            
        return vehicle_structure
    
    def load_vehicle_structure(self):
        """Load vehicle structure from file"""
        try:
            with open(self.vehicle_structure_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"⚠️ Could not load vehicle structure: {str(e)}")
            return None
            
    def extract_stage_data(self, vehicle_info):
        """Extract ALL tuning stage data - COMPLETE VERSION with all stages"""
        def _try_extract_complete_data():
            # Wait for any results to appear
            WebDriverWait(self.driver, 20).until(
                lambda driver: len(driver.find_elements(By.TAG_NAME, "table")) > 0
            )
            
            time.sleep(3)  # Wait for all stages to load completely
            
            # Find ALL tables on the page - each table is likely a different stage
            all_tables = self.driver.find_elements(By.TAG_NAME, "table")
            
            if not all_tables:
                raise Exception("No tuning data tables found")
            
            complete_data = {
                **vehicle_info,
                'scraped_at': datetime.now().isoformat(),
                'country': 'DE',  # Static country since we removed country rotation
                'stages': [],  # Will contain all available stages
                'total_stages': 0
            }
            
            print(f"    🔍 Found {len(all_tables)} table(s) on page")
            
            # Extract data from each table
            stages_found = 0
            for table_idx, table in enumerate(all_tables):
                try:
                    # Try to extract stage data from this table
                    stage_data = self._extract_single_stage_from_table(table, table_idx + 1)
                    
                    if stage_data:
                        complete_data['stages'].append(stage_data)
                        stages_found += 1
                        print(f"    📊 {stage_data['stage']}: {stage_data.get('power_tuned', 'N/A')} | {stage_data.get('torque_tuned', 'N/A')} | {stage_data.get('price_uvp', stage_data.get('price_offer', stage_data.get('price', 'N/A')))}")
                        
                except Exception as e:
                    # Not every table will be a stage table, continue
                    print(f"    ⚠️  Table {table_idx + 1} skipped: {str(e)[:50]}...")
                    continue
            
            complete_data['total_stages'] = stages_found
            
            if stages_found == 0:
                raise Exception("No valid stage data extracted from any table")
                
            return complete_data
        
        # Try extraction with intelligent retry
        result = self.intelligent_retry(_try_extract_complete_data, "Complete stage extraction")
        
        if result:
            # Track proxy usage for rotation
            self.proxy_rotation_count += 1
            stages_count = result.get('total_stages', 0)
            print(f"✅ {result['motor']}: {stages_count} stage(s) extracted")
            
            # Check if we should rotate proxy
            if self.should_rotate_proxy():
                print(f"🔄 Reached {self.vehicles_per_proxy_switch} vehicles, preparing proxy rotation...")
        
        return result
    
    def _extract_single_stage_from_table(self, table, table_index):
        """Extract data from a single table that represents one tuning stage"""
        try:
            # Get table text to analyze
            table_text = table.text.upper()
            
            # Determine stage number from table content
            stage_number = 1  # Default
            stage_name = f"Stage {table_index}"  # Fallback
            
            if "STAGE 1" in table_text or "SMA STAGE 1" in table_text:
                stage_number = 1
                stage_name = "Stage 1"
            elif "STAGE 2" in table_text or "SMA STAGE 2" in table_text:
                stage_number = 2
                stage_name = "Stage 2"
            elif "STAGE 3" in table_text or "SMA STAGE 3" in table_text:
                stage_number = 3
                stage_name = "Stage 3"
            elif "ORIGINAL" in table_text and "TUNING" in table_text:
                # This looks like a stage table, use table index
                stage_number = table_index
                stage_name = f"Stage {table_index}"
            else:
                # Not a stage table
                return None
                
            rows = table.find_elements(By.TAG_NAME, "tr")
            if len(rows) < 2:  # Need at least header + one data row
                return None
                
            stage_data = {
                'stage': stage_name,
                'stage_number': stage_number,
                'raw_table_text': table.text  # Keep original text for debugging
            }
            
            # Parse all table rows
            for row in rows:
                cells = row.find_elements(By.TAG_NAME, "td")
                if len(cells) < 3:  # Need at least: type, value1, value2
                    continue
                    
                row_type = cells[0].text.strip()
                
                # Skip header rows
                if any(header in row_type.upper() for header in ['ORIGINAL', 'NACH DEM TUNING', 'DIFFERENZ', 'PREIS']):
                    continue
                
                # Extract power data
                if any(keyword in row_type.upper() for keyword in ['LEISTUNG', 'POWER', 'PS', 'KW']):
                    if len(cells) >= 4:
                        original = cells[1].text.strip()
                        tuned = cells[2].text.strip()
                        difference = cells[3].text.strip()
                        
                        stage_data.update({
                            'power_original': original,
                            'power_tuned': tuned,
                            'power_difference': difference
                        })
                        
                        # Price might be in 4th or 5th column
                        if len(cells) >= 5:
                            price = cells[4].text.strip()
                            if 'UVP' in price.upper():
                                stage_data['price_uvp'] = price
                            elif 'ANGEBOT' in price.upper():
                                stage_data['price_offer'] = price
                            elif 'EUR' in price.upper() or 'ANFRAGEN' in price.upper():
                                stage_data['price'] = price
                            
                # Extract torque data
                elif any(keyword in row_type.upper() for keyword in ['DREHMOMENT', 'TORQUE', 'NM']):
                    if len(cells) >= 4:
                        original = cells[1].text.strip()
                        tuned = cells[2].text.strip()
                        difference = cells[3].text.strip()
                        
                        stage_data.update({
                            'torque_original': original,
                            'torque_tuned': tuned,
                            'torque_difference': difference
                        })
            
            # Validate that we have essential data (either power or torque)
            has_power = stage_data.get('power_original') and stage_data.get('power_tuned')
            has_torque = stage_data.get('torque_original') and stage_data.get('torque_tuned')
            
            if has_power or has_torque:
                return stage_data
            else:
                return None
                
        except Exception as e:
            # If extraction fails for this table, return None
            return None
            
    def run_complete_extraction(self):
        """Run the complete extraction process"""
        print("""
🧠 SMA INTELLIGENT Extraction - Best Strategy Combined
🔄 Intelligent retry: 1s → 2s → 4s → 8s → 10s (max)
🌍 Auto country rotation every 1500 vehicles
⚡ Fast when website is fast, patient when slow
🛡️ Maximum reliability with optimal speed
==================================================
        """)
        
        # Test the intelligent navigation
        if not self.setup_browser():
            print("❌ Failed to setup browser")
            return
            
        if not self.navigate_to_configurator():
            print("❌ Failed to load configurator")
            return
            
        print(f"✅ INTELLIGENT scraper ready!")
        print(f"🔄 Will auto-rotate proxies after {self.vehicles_per_proxy_switch} vehicles")
        
        # Add basic extraction test here
        def _test_vehicle_types():
            # Get vehicle types
            vehicle_types_select = Select(self.driver.find_element(By.ID, "vehicle_types"))
            vehicle_type_options = vehicle_types_select.options[1:]  # Skip "Bitte wählen"
            
            if not vehicle_type_options:
                raise Exception("No vehicle types found")
                
            print(f"📋 Found {len(vehicle_type_options)} vehicle types")
            
            # Test with first vehicle type
            first_vt = vehicle_type_options[0].text.strip()
            print(f"🚗 Testing with: {first_vt}")
            
            vehicle_type_options[0].click()
            time.sleep(2)  # Basic wait for selection
            
            return True
        
        try:
            test_result = self.intelligent_retry(_test_vehicle_types, "Vehicle type test")
            if test_result:
                print("✅ INTELLIGENT test successful - ready for full extraction!")
            else:
                print("❌ Test failed")
            
        except Exception as e:
            print(f"❌ Test failed: {str(e)}")
        finally:
            if hasattr(self, 'driver'):
                self.driver.quit()

    def run_full_extraction(self):
        """Run the complete INTELLIGENT extraction process"""
        try:
            start_time = datetime.now()
            
            if not self.setup_browser():
                return False
                
            if not self.navigate_to_configurator():
                return False
            
            print(f"🚀 FULL EXTRACTION started with proxy rotation")
            print(f"🔄 Auto-rotation every {self.vehicles_per_proxy_switch} vehicles")
            
            # Run the complete extraction
            success = self.extract_complete_data()
            
            if success:
                end_time = datetime.now()
                duration = end_time - start_time
                print(f"\n✅ INTELLIGENT extraction completed!")
                print(f"⏱️  Duration: {duration}")
                print(f"🚗 Total vehicles: {self.stats['vehicles_scraped']}")
                print(f"📊 Total stages: {self.stats['stages_extracted']}")
                print(f"❌ Errors: {self.stats['errors']}")
                
                # Final save
                self.save_progress()
                self.save_results()
                print(f"💾 Results saved: {self.results_file}")
                
        except KeyboardInterrupt:
            print("\n⚠️  Extraction interrupted by user")
            self.save_progress()
            self.save_results()
            print("💾 Progress saved - you can resume later")
            
        except Exception as e:
            print(f"\n❌ Extraction failed: {str(e)}")
            self.save_progress()
            
        finally:
            if hasattr(self, 'driver'):
                self.driver.quit()
                
        # Print final stats
        print(f"\n📈 FINAL STATS:")
        if 'resume_count' in self.stats:
            print(f"🔄 Resumed from: {self.stats['resume_count']} vehicles")
            print(f"🆕 New vehicles: {self.stats['vehicles_scraped'] - self.stats['resume_count']}")
        print(f"🚗 Total extracted: {self.stats['vehicles_scraped']}")
        print(f"❌ Total errors: {self.stats['errors']}")
        print(f"🔄 Progress file: {self.progress_file}")
        print(f"💾 Results file: {self.results_file}")
        
        print("\n🏁 Extraction complete!")
        print("✅ Resume functionality")
        print("✅ Progress tracking")
        print("✅ Multi-stage extraction")
        
    def extract_complete_data(self):
        """Extract all SMA tuning data - INTELLIGENT VERSION"""
        try:
            # Wait for configurator to fully load
            WebDriverWait(self.driver, 30).until(
                EC.presence_of_element_located((By.ID, "vehicle_types"))
            )
            time.sleep(3)  # INTELLIGENT: Extended wait for full page load
            
            # Scan and load vehicle structure first
            vehicle_structure = self.scan_and_save_vehicle_structure()
            if not vehicle_structure:
                print("❌ Could not load vehicle structure")
                return False
            
            total_combinations = 0
            processed_combinations = len(self.progress['completed_combinations'])
            
            print(f"\n🧠 INTELLIGENT extraction started...")
            print(f"🔄 Resume: {processed_combinations} already completed")
            print(f"⚡ Adaptive timing: Fast when possible, patient when needed")
            print(f"📋 Found {len(vehicle_structure)} vehicle types")
            
            # Find intelligent resume position based on current_position
            resume_position = self.find_resume_position()
            resume_reached = not resume_position  # Only start skipping after we reach resume position
            


            for vehicle_type_text, vehicle_type_data in vehicle_structure.items():
                vehicle_type_value = vehicle_type_data['value']
                brands_list = vehicle_type_data['brands']
                
                if not vehicle_type_value or not vehicle_type_text:
                    continue
                
                # Skip vehicle types that are completely processed (smart resume)
                if resume_position:
                    # Define processing order for vehicle types (not alphabetical!)
                    vehicle_type_order = [
                        "Kraftfahrzeuge (PKW)",
                        "Motorräder/Quads/Roller", 
                        "Jet Ski's",
                        "Motorboote",
                        "Lastkraftwagen (LKW)"
                    ]
                    
                    try:
                        current_index = vehicle_type_order.index(vehicle_type_text)
                        resume_index = vehicle_type_order.index(resume_position['vehicle_type'])
                        
                        # Skip if this vehicle type comes before our resume position in the defined order
                        if current_index < resume_index:
                            print(f"\n⏭️  Skipping: {vehicle_type_text} (already processed)")
                            continue
                        elif current_index > resume_index:
                            # We're past the resume position - process normally
                            pass
                        else:
                            # current_index == resume_index: We're at the exact resume vehicle type
                            # Only process if brand is empty (start of this vehicle type) or we have a specific brand position
                            pass
                    except ValueError:
                        # If vehicle type not in our predefined order, process it anyway
                        print(f"\n⚠️  Unknown vehicle type order: {vehicle_type_text} - processing anyway")
                        pass
                
                print(f"\n🚗 Processing: {vehicle_type_text}")
                
                # Select vehicle type with intelligent retry
                def _select_vehicle_type():
                    vt_select = Select(self.driver.find_element(By.ID, "vehicle_types"))
                    vt_select.select_by_value(vehicle_type_value)
                    return True
                
                if not self.intelligent_retry(_select_vehicle_type, f"Vehicle type selection: {vehicle_type_text}"):
                    continue
                
                # Wait for brands with intelligent retry
                def _wait_for_brands():
                    WebDriverWait(self.driver, 20).until(
                        lambda driver: len(Select(driver.find_element(By.ID, "vehicle_brands")).options) > 1
                    )
                    time.sleep(2)  # INTELLIGENT: Adaptive wait
                    return True
                
                if not self.intelligent_retry(_wait_for_brands, "Brands loading"):
                    continue
                
                # Use pre-loaded brands structure instead of DOM access
                for brand_data in brands_list:
                    brand_value = brand_data['value']
                    brand_text = brand_data['text']
                    
                    if not brand_value or not brand_text:
                        continue
                    
                    # Smart resume: Skip brands that come before our current position
                    if (not resume_reached and resume_position and 
                        vehicle_type_text == resume_position['vehicle_type']):
                        cp = self.progress.get('current_position', {})
                        if brand_text < cp.get('brand', ''):
                            print(f"  ⏭️  Skipping: {brand_text} (before current position)")
                            continue
                        elif brand_text == cp.get('brand', ''):
                            # We're at the current brand - continue from current position within this brand
                            pass  # Continue with current brand logic
                        elif brand_text > cp.get('brand', ''):
                            # We've passed the current brand, start processing all future brands normally
                            resume_reached = True
                    
                    print(f"  🏭 Brand: {brand_text}")
                    
                    # Check if we should rotate proxy
                    if self.should_rotate_proxy():
                        if not self.rotate_proxy():
                            print("❌ Proxy rotation failed")
                            return False
                        # Need to restart the whole selection process from vehicle type
                        return self.extract_complete_data()
                    
                    try:
                        # IMPORTANT: Always ensure vehicle type is selected first
                        # This is critical after proxy rotations/session recovery
                        def _ensure_vehicle_type_selected():
                            vehicle_types_select = Select(self.driver.find_element(By.ID, "vehicle_types"))
                            current_selection = vehicle_types_select.first_selected_option.get_attribute('value')
                            if current_selection != vehicle_type_value:
                                vehicle_types_select.select_by_value(vehicle_type_value)
                                time.sleep(3)  # Wait for brands to load
                                return True
                            return True
                        
                        # Ensure vehicle type is correctly selected
                        if not self.intelligent_retry(_ensure_vehicle_type_selected, f"Vehicle type selection: {vehicle_type_text}"):
                            print(f"  ❌ Failed to select vehicle type {vehicle_type_text} - skipping brand {brand_text}")
                            continue
                        
                        # Select brand with intelligent retry
                        def _select_brand():
                            # Wait a bit for brands to load
                            time.sleep(2)
                            # Check if brands dropdown is ready
                            brands_select = Select(self.driver.find_element(By.ID, "vehicle_brands"))
                            if len(brands_select.options) <= 1:
                                raise Exception("Brands not loaded yet")
                            brands_select.select_by_value(brand_value)
                            return True
                        
                        if not self.intelligent_retry(_select_brand, f"Brand selection: {brand_text}"):
                            print("🔄 Brand selection failed - attempting session recovery...")
                            if self.rotate_proxy():
                                print("✅ Session recovery successful - restarting extraction")
                                return self.extract_complete_data()  # Restart complete extraction
                            else:
                                print("❌ Session recovery failed - skipping to next brand")
                                continue
                        
                        # Wait for models with intelligent retry
                        def _wait_for_models():
                            WebDriverWait(self.driver, 20).until(
                                lambda driver: len(Select(driver.find_element(By.ID, "vehicle_model")).options) > 1
                            )
                            time.sleep(2)  # INTELLIGENT
                            return True
                        
                        if not self.intelligent_retry(_wait_for_models, "Models loading"):
                            print(f"❌ Models loading failed after 5 attempts for {brand_text}")
                            print(f"⚠️  No models available for {brand_text} - trying session recovery first...")
                            
                            # Try session recovery once
                            if self.rotate_proxy():
                                print("✅ Session recovery successful - restarting extraction")
                                return self.extract_complete_data()  # Restart complete extraction
                            else:
                                print(f"❌ Session recovery failed - skipping brand {brand_text}")
                                
                                # Create a dummy combination ID for this brand without models
                                combination_id = self.create_combination_id(
                                    vehicle_type_text, brand_text, "NO_MODELS_AVAILABLE", "NO_MODELS_AVAILABLE", "NO_MODELS_AVAILABLE"
                                )
                                self.progress['completed_combinations'].add(combination_id)
                                
                                # Save progress immediately so this brand is skipped next time
                                self.save_progress()
                                
                                # Continue to next brand
                                continue
                        
                        # Get models
                        models_select = Select(self.driver.find_element(By.ID, "vehicle_model"))
                        model_options = models_select.options[1:]  # Skip "Bitte wählen"
                        
                        for model_option in model_options:
                            model_value = model_option.get_attribute('value')
                            model_text = model_option.text.strip()
                            
                            if not model_value or not model_text:
                                continue
                            
                            # Check if this model was already marked as problematic and should be skipped
                            model_error_id = self.create_combination_id(
                                vehicle_type_text, brand_text, model_text, "MODEL_ERROR", "MODEL_ERROR"
                            )
                            no_years_id = self.create_combination_id(
                                vehicle_type_text, brand_text, model_text, "NO_YEARS_AVAILABLE", "NO_YEARS_AVAILABLE"
                            )
                            
                            if (model_error_id in self.progress['completed_combinations'] or 
                                no_years_id in self.progress['completed_combinations']):
                                print(f"    ⏭️  Model: {model_text} (marked as problematic, skipping)")
                                continue
                            
                            # Smart resume: Skip models before current position  
                            if (not resume_reached and brand_text == self.progress.get('current_position', {}).get('brand', '')):
                                cp = self.progress.get('current_position', {})
                                if model_text < cp.get('model', ''):
                                    print(f"    ⏭️  Model: {model_text} (before current position)")
                                    continue
                                elif model_text == cp.get('model', ''):
                                    # We're at the current model - continue from current position within this model
                                    pass  # Continue with current model logic  
                                elif model_text > cp.get('model', ''):
                                    # We've passed the current model, process all future models normally
                                    resume_reached = True
                            
                            print(f"    📱 Model: {model_text}")
                            
                            try:
                                # Select model with intelligent retry
                                def _select_model():
                                    models_select.select_by_value(model_value)
                                    return True
                                
                                if not self.intelligent_retry(_select_model, f"Model selection: {model_text}"):
                                    continue
                                
                                # Wait for years with intelligent retry
                                def _wait_for_years():
                                    WebDriverWait(self.driver, 20).until(
                                        lambda driver: len(Select(driver.find_element(By.ID, "vehicle_year")).options) > 1
                                    )
                                    time.sleep(2)  # INTELLIGENT
                                    return True
                                
                                if not self.intelligent_retry(_wait_for_years, "Years loading"):
                                    print(f"❌ Years loading failed after 5 attempts for {model_text}")
                                    print(f"⚠️  No years available for {brand_text} {model_text} - skipping model")
                                    
                                    # Create a dummy combination ID for this model without years
                                    combination_id = self.create_combination_id(
                                        vehicle_type_text, brand_text, model_text, "NO_YEARS_AVAILABLE", "NO_YEARS_AVAILABLE"
                                    )
                                    self.progress['completed_combinations'].add(combination_id)
                                    
                                    # Save progress immediately so this model is skipped next time
                                    self.save_progress()
                                    
                                    # Continue to next model
                                    continue
                                
                                # Get years
                                years_select = Select(self.driver.find_element(By.ID, "vehicle_year"))
                                year_options = years_select.options[1:]  # Skip "Bitte wählen"
                                
                                for year_option in year_options:
                                    year_value = year_option.get_attribute('value')
                                    year_text = year_option.text.strip()
                                    
                                    if not year_value or not year_text:
                                        continue
                                    
                                    print(f"      📅 Year: {year_text} -> ", end="")
                                    
                                    try:
                                        # Select year with intelligent retry
                                        def _select_year():
                                            years_select.select_by_value(year_value)
                                            return True
                                        
                                        if not self.intelligent_retry(_select_year, f"Year selection: {year_text}"):
                                            print("❌")
                                            continue
                                        
                                        # Wait for motors with intelligent retry
                                        motors_loaded = False
                                        motors_attempts = 0
                                        max_motors_attempts = 5
                                        
                                        while not motors_loaded and motors_attempts < max_motors_attempts:
                                            motors_attempts += 1
                                            try:
                                                WebDriverWait(self.driver, 10).until(
                                                    lambda driver: len(Select(driver.find_element(By.ID, "vehicle_motor")).options) > 1
                                                )
                                                time.sleep(1)
                                                motors_loaded = True
                                                print("✅", end="")
                                            except Exception as e:
                                                print(f"⚠️  Motors loading failed (attempt {motors_attempts})")
                                                if motors_attempts < max_motors_attempts:
                                                    print(f"    Retrying in {motors_attempts}s...")
                                                    time.sleep(motors_attempts)
                                                    # After timeout, try to reselect the vehicle to recover
                                                    try:
                                                        # Re-select vehicle type
                                                        vehicle_types_select = Select(self.driver.find_element(By.ID, "vehicle_types"))
                                                        vehicle_types_select.select_by_value(vehicle_type_value)
                                                        time.sleep(1)
                                                        
                                                        # Re-select brand
                                                        brands_select = Select(self.driver.find_element(By.ID, "vehicle_brands"))
                                                        brands_select.select_by_value(brand_value)
                                                        time.sleep(1)
                                                        
                                                        # Re-select model
                                                        models_select = Select(self.driver.find_element(By.ID, "vehicle_model"))
                                                        models_select.select_by_value(model_value)
                                                        time.sleep(1)
                                                        
                                                        # Re-select year
                                                        years_select = Select(self.driver.find_element(By.ID, "vehicle_year"))
                                                        years_select.select_by_value(year_value)
                                                        time.sleep(1)
                                                    except Exception as recovery_error:
                                                        print(f"    Recovery failed: {recovery_error}")
                                        
                                        if not motors_loaded:
                                            print("❌ Motors loading failed after 5 attempts")
                                            # Motors loading failed after 5 attempts - mark vehicle as completed
                                            # This way it will be skipped in future runs
                                            print(f"⚠️  No motors available for {brand_text} {model_text} {year_text} - marking as completed")
                                            
                                            # Create a dummy combination ID for this vehicle without motor
                                            combination_id = self.create_combination_id(
                                                vehicle_type_text, brand_text, model_text, year_text, "NO_MOTORS_AVAILABLE"
                                            )
                                            self.progress['completed_combinations'].add(combination_id)
                                            
                                            # Save progress immediately so this vehicle is skipped next time
                                            self.save_progress()
                                            
                                            # Continue to next year (no need to break since elements should still be valid)
                                            continue
                                        
                                        # Get motors
                                        motors_select = Select(self.driver.find_element(By.ID, "vehicle_motor"))
                                        motor_options = motors_select.options[1:]  # Skip "Bitte wählen"
                                        
                                        for motor_option in motor_options:
                                            motor_value = motor_option.get_attribute('value')
                                            motor_text = motor_option.text.strip()
                                            
                                            if not motor_value or not motor_text:
                                                continue
                                            
                                            total_combinations += 1
                                            
                                            # Create combination ID for resume check
                                            combination_id = self.create_combination_id(
                                                vehicle_type_text, brand_text, model_text, year_text, motor_text
                                            )
                                            
                                            # Smart resume: Skip to exact position instead of checking all combinations
                                            if not resume_reached:
                                                # Check if we're at the exact resume position
                                                cp = self.progress.get('current_position', {})
                                                if (cp.get('vehicle_type') == vehicle_type_text and
                                                    cp.get('brand') == brand_text and
                                                    cp.get('model') == model_text and
                                                    cp.get('year') == year_text and
                                                    cp.get('motor') == motor_text):
                                                    resume_reached = True  # Found exact position, start processing next
                                                    print("🎯", end="")
                                                    continue  # Skip this exact combination and start with next
                                                else:
                                                    print("⏭️", end="")
                                                    continue
                                            
                                            # Skip if already processed
                                            if combination_id in self.progress['completed_combinations']:
                                                print(f"⏭️", end="")
                                                continue
                                                
                                            try:
                                                # Select motor with intelligent retry
                                                def _select_motor():
                                                    motors_select.select_by_value(motor_value)
                                                    time.sleep(3)  # INTELLIGENT: Wait for data loading
                                                    return True
                                                
                                                if not self.intelligent_retry(_select_motor, f"Motor selection: {motor_text}"):
                                                    print(f"❌ {motor_text}")
                                                    continue
                                                
                                                # Extract stage data
                                                vehicle_info = {
                                                    'vehicle_type': vehicle_type_text,
                                                    'brand': brand_text,
                                                    'model': model_text,
                                                    'year': year_text,
                                                    'motor': motor_text
                                                }
                                                
                                                stage_data = self.extract_stage_data(vehicle_info)
                                                
                                                if stage_data:
                                                    self.extracted_data.append(stage_data)
                                                    self.progress['completed_combinations'].add(combination_id)
                                                    
                                                    # Update current position for smart resume
                                                    self.progress['current_position'] = {
                                                        'vehicle_type': vehicle_type_text,
                                                        'brand': brand_text,
                                                        'model': model_text,
                                                        'year': year_text,
                                                        'motor': motor_text
                                                    }
                                                    
                                                    self.stats['vehicles_scraped'] += 1
                                                    self.stats['stages_extracted'] += stage_data.get('total_stages', 0)
                                                    processed_combinations += 1
                                                    
                                                    print(f"✅ {motor_text}: ({processed_combinations})")
                                                    
                                                    # Save progress every 10 vehicles
                                                    if processed_combinations % 10 == 0:
                                                        self.save_progress()
                                                        self.save_results()
                                                        print(f"        💾 INTELLIGENT Progress saved ({processed_combinations} vehicles)")
                                                        
                                                else:
                                                    print(f"⚠️  {motor_text}: No tuning data")
                                                    
                                            except Exception as e:
                                                print(f"❌ {motor_text}: {str(e)}")
                                                self.stats['errors'] += 1
                                        
                                        print()  # New line after year
                                                
                                    except Exception as e:
                                        print(f"❌ Year error: {str(e)}")
                                        continue
                                        
                            except Exception as e:
                                print(f"    ❌ Model error: {str(e)}")
                                # Don't mark as completed immediately - let the brand-level retry handle it
                                continue
                        

                        
                        # Reset retry counter for successful brand processing
                        brand_retry_key = f"{vehicle_type_text}_{brand_text}"
                        if hasattr(self, 'brand_retry_count') and brand_retry_key in self.brand_retry_count:
                            self.brand_retry_count[brand_retry_key] = 0
                        
                        # Important: After processing the current brand completely, 
                        # set resume_reached = True so all future brands are processed
                        if not resume_reached and brand_text == self.progress.get('current_position', {}).get('brand', ''):
                            resume_reached = True
                            print(f"  ✅ Completed current brand {brand_text} - will process all future brands")
                            
                            # Show what brands are coming next for debugging
                            current_brand_index = next((i for i, bd in enumerate(brands_list) if bd['text'] == brand_text), -1)
                            if current_brand_index >= 0 and current_brand_index < len(brands_list) - 1:
                                next_brands = [bd['text'] for bd in brands_list[current_brand_index + 1:current_brand_index + 4]]
                                print(f"  📋 Next brands to process: {', '.join(next_brands)}...")
                                
                    except Exception as e:
                        error_msg = str(e)
                        print(f"  ❌ Brand error: {error_msg}")
                        
                        # Track retry attempts for this brand
                        brand_retry_key = f"{vehicle_type_text}_{brand_text}"
                        if not hasattr(self, 'brand_retry_count'):
                            self.brand_retry_count = {}
                        
                        self.brand_retry_count[brand_retry_key] = self.brand_retry_count.get(brand_retry_key, 0) + 1
                        current_retry = self.brand_retry_count[brand_retry_key]
                        
                        print(f"  🔄 Brand retry attempt {current_retry}/5 for {brand_text}")
                        
                        if current_retry < 5:
                            # Check for critical errors that require proxy rotation
                            if ("stale element reference" in error_msg.lower() or 
                                "session info" in error_msg.lower() or
                                "chrome" in error_msg.lower()):
                                print("🔄 Critical error detected - forcing proxy rotation...")
                                if self.rotate_proxy():
                                    print("✅ Proxy rotation successful - retrying brand")
                                    return self.extract_complete_data()  # Restart from beginning
                                else:
                                    print("❌ Proxy rotation failed - ending extraction")
                                    return False
                            else:
                                # Non-critical error, just continue to next brand
                                continue
                        else:
                            # After 5 attempts, mark problematic models as completed
                            print(f"  ❌ Brand {brand_text} failed after 5 attempts - marking problematic models as completed")
                            
                            # Check if we're in the middle of processing a specific model
                            cp = self.progress.get('current_position', {})
                            if (cp.get('vehicle_type') == vehicle_type_text and 
                                cp.get('brand') == brand_text):
                                
                                # If we have a specific model that's causing issues, mark it as completed
                                current_model = cp.get('model')
                                if current_model:
                                    print(f"  🔧 Marking problematic model '{current_model}' as completed to prevent loop")
                                    # Create a dummy combination ID for this problematic model
                                    combination_id = self.create_combination_id(
                                        vehicle_type_text, brand_text, current_model, "MODEL_ERROR", "MODEL_ERROR"
                                    )
                                    self.progress['completed_combinations'].add(combination_id)
                                    self.save_progress()
                            
                            # Reset retry counter for this brand
                            self.brand_retry_count[brand_retry_key] = 0
                            continue
            
            return True
                        
        except Exception as e:
            error_msg = str(e)
            print(f"❌ Main extraction error: {error_msg}")
            
            # Check for critical errors that require proxy rotation
            if ("stale element reference" in error_msg.lower() or 
                "session info" in error_msg.lower() or
                "chrome" in error_msg.lower()):
                print("🔄 Critical main error detected - forcing proxy rotation...")
                if self.rotate_proxy():
                    print("✅ Proxy rotation successful - restarting extraction")
                    return self.extract_complete_data()  # Restart from beginning
                else:
                    print("❌ Proxy rotation failed - ending extraction")
                    return False
            return False

    # ---------------------------------------------------------------------
    # Proxy helpers
    # ---------------------------------------------------------------------
    def _load_proxy_list(self) -> list[str]:
        """Read iproyal-proxies.csv and return list of fully formed proxy URLs."""
        proxies: list[str] = []
        try:
            if not os.path.exists(self.proxy_csv_path):
                print(f"⚠️  Proxy CSV not found: {self.proxy_csv_path}")
                return proxies

            with open(self.proxy_csv_path, "r", encoding="utf-8") as f:
                for raw in f:
                    raw = raw.strip()
                    if not raw:
                        continue
                    # Format: host:port:user:password (colon-separated)
                    parts = raw.split(":")
                    if len(parts) < 4:
                        continue
                    host, port, user = parts[0].strip(), parts[1].strip(), parts[2].strip()
                    password = ":".join(parts[3:]).strip()  # Password might contain colons
                    proxy_url = f"http://{user}:{password}@{host}:{port}"
                    proxies.append(proxy_url)
        except Exception as e:
            print(f"⚠️  Could not load proxy list: {e}")

        if not proxies:
            print("❌ No proxies available – scraping cannot start.")
        else:
            print(f"🔑 Loaded {len(proxies)} proxies from CSV")
        return proxies

    def _get_next_proxy(self) -> str | None:
        """Return the next proxy URL from list (round-robin)."""
        if not self.proxy_list:
            return None
        proxy_url = self.proxy_list[self.proxy_index % len(self.proxy_list)]
        self.proxy_index += 1
        return proxy_url

def main():
    import sys
    
    # Check for extraction mode
    extract_mode = '--extract' in sys.argv
    use_proxy = '--no-proxy' not in sys.argv
    
    if extract_mode:
        print("🚀 STARTING FULL EXTRACTION MODE")
        extractor = SMASlowReadyExtractor(use_proxy=use_proxy)
        extractor.run_full_extraction()
    else:
        print("🧪 RUNNING TEST MODE (use --extract for full extraction)")
        extractor = SMASlowReadyExtractor(use_proxy=False)
        extractor.run_complete_extraction()

if __name__ == "__main__":
    main() 