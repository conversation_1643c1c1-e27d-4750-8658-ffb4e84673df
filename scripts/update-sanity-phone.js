import { createClient } from '@sanity/client'

const projectId = 'uzktk1gg'
const token = 'skWd17eHPh2Xn8PHQFsv8yZEWQBcmzjvWGlqNlE9trX3czS146WY9D4u348SYUv81nBNdTxNe4oNhoDy0YYoBI2jcARk03IghSmiXqKKaVlsjTJky1L5j6gE6UgNwCIOAxqN0Wg0AmpbIkMmJge9vwnfUYyd90fNgkLkAHkGNq4zA5SYfTRq'

async function updateSanityPhone() {
  const datasets = ['development', 'production']
  const newPhoneNumber = '+41 76 250 07 61'
  
  for (const dataset of datasets) {
    console.log(`\n📞 Updating phone number in ${dataset} dataset...`)
    
    const client = createClient({
      projectId,
      dataset,
      token,
      useCdn: false,
      apiVersion: '2024-01-01'
    })

    try {
      // Get site settings
      const siteSettings = await client.fetch('*[_type == "siteSettings"][0]')
      
      if (!siteSettings) {
        console.log(`❌ No site settings found in ${dataset}`)
        continue
      }

      console.log(`📋 Current phone in ${dataset}:`, siteSettings.footer?.phone || 'Not set')
      
      // Show what we would update (read-only token)
      console.log(`📝 Would update to: ${newPhoneNumber}`)
      console.log(`📄 Document ID: ${siteSettings._id}`)
      
      // Show current footer data
      if (siteSettings.footer) {
        console.log(`📊 Current footer data:`)
        console.log(`   Company: ${siteSettings.footer.companyName}`)
        console.log(`   Email: ${siteSettings.footer.email}`)
        console.log(`   WhatsApp: ${siteSettings.footer.whatsapp}`)
        console.log(`   Phone: ${siteSettings.footer.phone || 'NOT SET'}`)
      }
      
    } catch (error) {
      console.error(`❌ Error checking ${dataset}:`, error.message)
    }
  }
  
  console.log('\n📋 Summary:')
  console.log('✅ strings.ts has been updated with correct phone number')
  console.log('✅ Fallback data now matches Sanity data')
  console.log('⚠️  Production dataset missing phone number in footer')
  console.log('💡 Recommendation: Add phone number manually in Sanity Studio')
}

updateSanityPhone().catch(console.error)
