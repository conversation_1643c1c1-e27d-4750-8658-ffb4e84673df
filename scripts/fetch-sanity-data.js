import { createClient } from '@sanity/client'

const projectId = 'uzktk1gg'
const token = 'skWd17eHPh2Xn8PHQFsv8yZEWQBcmzjvWGlqNlE9trX3czS146WY9D4u348SYUv81nBNdTxNe4oNhoDy0YYoBI2jcARk03IghSmiXqKKaVlsjTJky1L5j6gE6UgNwCIOAxqN0Wg0AmpbIkMmJge9vwnfUYyd90fNgkLkAHkGNq4zA5SYfTRq'

async function fetchSanityData() {
  const datasets = ['development', 'production']
  
  for (const dataset of datasets) {
    console.log(`\n📊 Fetching data from ${dataset} dataset...`)
    
    const client = createClient({
      projectId,
      dataset,
      token,
      useCdn: false,
      apiVersion: '2024-01-01'
    })

    try {
      // Fetch all document types
      const allDocs = await client.fetch('*[0...20] { _type, _id, title, name }')
      console.log(`📄 Found ${allDocs.length} documents:`)
      
      const docTypes = {}
      allDocs.forEach(doc => {
        if (!docTypes[doc._type]) docTypes[doc._type] = []
        docTypes[doc._type].push(doc)
      })
      
      Object.keys(docTypes).forEach(type => {
        console.log(`\n  📋 ${type} (${docTypes[type].length} documents):`)
        docTypes[type].forEach(doc => {
          console.log(`    - ${doc._id}: ${doc.title || doc.name || 'No title'}`)
        })
      })

      // Fetch specific content types
      console.log(`\n🔍 Detailed content from ${dataset}:`)
      
      // Site Settings
      const siteSettings = await client.fetch('*[_type == "siteSettings"][0]')
      if (siteSettings) {
        console.log(`\n⚙️  Site Settings:`)
        console.log(`   Site Name: ${siteSettings.siteName || 'Not set'}`)
        console.log(`   Navigation:`, siteSettings.navigation || 'Not set')
        console.log(`   Footer:`, siteSettings.footer || 'Not set')
      }

      // Homepage
      const homepage = await client.fetch('*[_type == "homepage"][0]')
      if (homepage) {
        console.log(`\n🏠 Homepage:`)
        console.log(`   Hero Title: ${homepage.heroTitle || 'Not set'}`)
        console.log(`   Hero Subtitle: ${homepage.heroSubtitle || 'Not set'}`)
        console.log(`   Features:`, homepage.features?.length || 0, 'items')
      }

      // Services Page
      const servicesPage = await client.fetch('*[_type == "servicesPage"][0]')
      if (servicesPage) {
        console.log(`\n🛠️  Services Page:`)
        console.log(`   Hero Title: ${servicesPage.heroTitle || 'Not set'}`)
        console.log(`   Services:`, servicesPage.services?.length || 0, 'items')
      }

      // Services
      const services = await client.fetch('*[_type == "service"]')
      console.log(`\n🔧 Services: ${services.length} items`)
      services.forEach(service => {
        console.log(`   - ${service.title}: ${service.description?.substring(0, 50) || 'No description'}...`)
      })

    } catch (error) {
      console.error(`❌ Error fetching from ${dataset}:`, error.message)
    }
  }
}

fetchSanityData().catch(console.error)
