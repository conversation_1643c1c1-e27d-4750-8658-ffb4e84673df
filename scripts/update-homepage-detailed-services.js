import { createClient } from '@sanity/client'

const projectId = 'uzktk1gg'
const adminToken = 'skkr72eqIhavsdBJHo4bW4tgRcgD0PJQ0LYieeR5ScPe3yiOPE5jCDXFke9w09FdAVItL43v1Z14CsSoq'

const detailedServicesData = [
  {
    _type: 'serviceSection',
    _key: 'software-tuning',
    title: 'Software-Tuning',
    order: 1,
    services: [
      {
        _type: 'serviceItem',
        _key: 'kennfeldoptimierung',
        title: 'Kennfeldoptimierung',
        description: 'Professionelle Optimierung der Motorsteuerung für mehr Leistung und Effizienz.'
      },
      {
        _type: 'serviceItem',
        _key: 'leistungssteigerung',
        title: 'Leistungssteigerung',
        description: 'Bis zu 30% mehr Leistung und Drehmoment durch professionelle Software-Optimierung.'
      },
      {
        _type: 'serviceItem',
        _key: 'kraftstoffoptimierung',
        title: 'Kraftstoffoptimierung',
        description: 'Verbesserter Kraftstoffverbrauch bei gleichzeitig gesteigerter Leistung.'
      }
    ]
  },
  {
    _type: 'serviceSection',
    _key: 'fahrzeugaufbereitung',
    title: 'Fahrzeugaufbereitung',
    order: 2,
    services: [
      {
        _type: 'serviceItem',
        _key: 'lackpolitur',
        title: 'Lackpolitur',
        description: 'Professionelle Entfernung von Kratzern und Hologrammen für perfekte Lacktiefe.'
      },
      {
        _type: 'serviceItem',
        _key: 'keramik-versiegelung',
        title: 'Keramik-Versiegelung',
        description: 'Langanhaltender Schutz mit hochwertiger Keramikbeschichtung für bis zu 5 Jahre.'
      },
      {
        _type: 'serviceItem',
        _key: 'innenreinigung',
        title: 'Innenreinigung',
        description: 'Gründliche Reinigung und Aufbereitung des Fahrzeuginnenraums.'
      }
    ]
  },
  {
    _type: 'serviceSection',
    _key: 'garagen-service',
    title: 'Garagen-Service',
    order: 3,
    services: [
      {
        _type: 'serviceItem',
        _key: 'oelservice',
        title: 'Ölservice',
        description: 'Professioneller Motorölwechsel mit hochwertigen Ölen und Originalfiltern für optimale Motorleistung.'
      },
      {
        _type: 'serviceItem',
        _key: 'getriebeservice',
        title: 'Getriebeservice',
        description: 'Wartung und Reparatur von Schalt- und Automatikgetrieben für reibungslose Kraftübertragung.'
      },
      {
        _type: 'serviceItem',
        _key: 'bremsenservice',
        title: 'Bremsenservice',
        description: 'Kontrolle und Austausch von Bremsbelägen, Bremsscheiben und Bremsflüssigkeit für Ihre Sicherheit.'
      },
      {
        _type: 'serviceItem',
        _key: 'allgemeine-reparaturen',
        title: 'Allgemeine Reparaturen',
        description: 'Diagnose und Reparatur von Motorproblemen, Elektronik und anderen Fahrzeugkomponenten.'
      },
      {
        _type: 'serviceItem',
        _key: 'inspektion-wartung',
        title: 'Inspektion & Wartung',
        description: 'Regelmässige Fahrzeuginspektion und vorbeugende Wartung nach Herstellervorgaben.'
      },
      {
        _type: 'serviceItem',
        _key: 'klimaanlagen-service',
        title: 'Klimaanlagen-Service',
        description: 'Wartung, Befüllung und Reparatur von Klimaanlagen für optimalen Fahrkomfort.'
      }
    ]
  },
  {
    _type: 'serviceSection',
    _key: 'service-garantie',
    title: 'Service & Garantie',
    order: 4,
    services: [
      {
        _type: 'serviceItem',
        _key: 'zertifizierte-techniker',
        title: 'Zertifizierte Techniker',
        description: 'Ausgebildete Fachkräfte mit jahrelanger Erfahrung in der Fahrzeugoptimierung.'
      },
      {
        _type: 'serviceItem',
        _key: 'modernste-ausruestung',
        title: 'Modernste Ausrüstung',
        description: 'Neueste Technologie und professionelle Werkzeuge für beste Ergebnisse.'
      },
      {
        _type: 'serviceItem',
        _key: 'schneller-service',
        title: 'Schneller Service',
        description: 'Terminvereinbarung innerhalb von 24 Stunden und zügige Bearbeitung.'
      }
    ]
  }
]

async function updateHomepageDetailedServices() {
  const datasets = ['development', 'production']
  
  for (const dataset of datasets) {
    console.log(`\n🏠 Updating homepage detailed services in ${dataset} dataset...`)
    
    const client = createClient({
      projectId,
      dataset,
      token: adminToken,
      useCdn: false,
      apiVersion: '2024-01-01'
    })

    try {
      // Get existing homepage
      const homepage = await client.fetch('*[_type == "homepage"][0]')
      
      if (!homepage) {
        console.log(`❌ No homepage found in ${dataset}`)
        continue
      }

      console.log(`📋 Current homepage ID: ${homepage._id}`)
      
      // Update homepage with detailed services
      const result = await client
        .patch(homepage._id)
        .set({
          detailedServices: detailedServicesData
        })
        .commit()

      console.log(`✅ Successfully updated homepage in ${dataset} dataset`)
      console.log(`📄 Added ${detailedServicesData.length} service sections`)
      
      // Show what was added
      detailedServicesData.forEach(section => {
        console.log(`   - ${section.title}: ${section.services.length} services`)
      })
      
    } catch (error) {
      console.error(`❌ Error updating ${dataset}:`, error.message)
    }
  }
  
  console.log('\n🎉 Homepage detailed services update completed!')
  console.log('\n📝 Next steps:')
  console.log('1. Update the homepage component to use Sanity data')
  console.log('2. Remove hardcoded detailed services from page.tsx')
  console.log('3. Test the new Sanity-managed content')
}

updateHomepageDetailedServices().catch(console.error)
