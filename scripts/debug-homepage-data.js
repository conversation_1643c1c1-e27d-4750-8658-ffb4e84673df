import { createClient } from '@sanity/client'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config({ path: '.env.local' })

const projectId = process.env.NEXT_PUBLIC_SANITY_PROJECT_ID
const token = process.env.SANITY_API_READ_TOKEN

const client = createClient({
  projectId,
  dataset: 'development',
  useCdn: false,
  token,
  apiVersion: '2024-01-01',
})

async function debugHomepageData() {
  try {
    console.log('🔍 Fetching homepage data from Sanity...')
    
    const homepageData = await client.fetch(`
      *[_type == "homepage"][0]{
        _id,
        heroTitle,
        heroSubtitle,
        heroDescription,
        swipingServices[] | order(order asc) {
          text,
          order,
          _key
        }
      }
    `)
    
    console.log('📄 Homepage data:', JSON.stringify(homepageData, null, 2))
    
    if (homepageData?.swipingServices) {
      console.log('✅ Swiping services found:', homepageData.swipingServices.length)
      homepageData.swipingServices.forEach((service, index) => {
        console.log(`  ${index + 1}. "${service.text}" (order: ${service.order})`)
      })
    } else {
      console.log('❌ No swiping services found!')
    }
    
  } catch (error) {
    console.error('❌ Error fetching homepage data:', error)
  }
}

debugHomepageData()
