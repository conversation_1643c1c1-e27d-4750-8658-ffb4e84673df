import { createClient } from '@sanity/client'
import { createInterface } from 'readline'

// Environment variables
const projectId = process.env.NEXT_PUBLIC_SANITY_PROJECT_ID
const token = process.env.SANITY_API_READ_TOKEN
const dataset = process.env.NEXT_PUBLIC_SANITY_DATASET || 'development'

if (!projectId || !token) {
  throw new Error('Missing Sanity environment variables')
}

const client = createClient({
  projectId,
  dataset,
  apiVersion: '2024-01-01',
  token,
  useCdn: false,
})

const rl = createInterface({
  input: process.stdin,
  output: process.stdout,
})

async function updateWhatsAppNumber() {
  try {
    console.log(`🔄 Updating WhatsApp number in dataset: ${dataset}`)
    
    // Fetch current site settings
    const siteSettings = await client.fetch('*[_type == "siteSettings"][0]')
    
    if (!siteSettings) {
      console.error('❌ Site settings not found. Please run seed-site-settings.ts first.')
      return
    }
    
    console.log('\n📱 Current WhatsApp number:', siteSettings.footer?.whatsapp || 'Not set')
    console.log('\nFormat: +41XXXXXXXXX (international format without spaces)')
    console.log('Example: +***********')
    
    return new Promise<void>((resolve) => {
      rl.question('\n💬 Enter new WhatsApp Business number: ', async (whatsappNumber) => {
        if (!whatsappNumber.trim()) {
          console.log('❌ No number provided. Cancelled.')
          rl.close()
          resolve()
          return
        }
        
        // Validate format
        const phoneRegex = /^\+41\d{9}$/
        if (!phoneRegex.test(whatsappNumber.trim())) {
          console.log('❌ Invalid format. Please use +41XXXXXXXXX format (Swiss number)')
          rl.close()
          resolve()
          return
        }
        
        try {
          // Update the site settings
          const result = await client
            .patch(siteSettings._id)
            .set({
              'footer.whatsapp': whatsappNumber.trim(),
              'lastUpdated': new Date().toISOString(),
            })
            .commit()
          
          console.log('✅ WhatsApp number updated successfully!')
          console.log('📱 New number:', whatsappNumber.trim())
          console.log('📊 Dataset:', dataset)
          console.log('🆔 Document ID:', result._id)
          
          // Also update strings.ts as fallback
          console.log('\n💡 Remember to also update src/strings.ts for fallback:')
          console.log(`whatsapp: '${whatsappNumber.trim()}',`)
          
        } catch (error) {
          console.error('❌ Error updating WhatsApp number:', error)
        }
        
        rl.close()
        resolve()
      })
    })
    
  } catch (error) {
    console.error('❌ Error:', error)
  }
}

// Support for different datasets
async function chooseDataset() {
  if (process.argv.includes('--production')) {
    process.env.NEXT_PUBLIC_SANITY_DATASET = 'production'
    await updateWhatsAppNumber()
    return
  }
  
  if (process.argv.includes('--development')) {
    process.env.NEXT_PUBLIC_SANITY_DATASET = 'development'
    await updateWhatsAppNumber()
    return
  }
  
  console.log('\n📊 Which dataset do you want to update?')
  console.log('1. Development')
  console.log('2. Production')
  console.log('3. Both')
  
  return new Promise<void>((resolve) => {
    rl.question('\nEnter choice (1/2/3): ', async (choice) => {
      switch (choice.trim()) {
        case '1':
          process.env.NEXT_PUBLIC_SANITY_DATASET = 'development'
          await updateWhatsAppNumber()
          break
        case '2':
          process.env.NEXT_PUBLIC_SANITY_DATASET = 'production'
          await updateWhatsAppNumber()
          break
        case '3':
          console.log('\n🔄 Updating development dataset...')
          process.env.NEXT_PUBLIC_SANITY_DATASET = 'development'
          await updateWhatsAppNumber()
          
          console.log('\n🔄 Updating production dataset...')
          process.env.NEXT_PUBLIC_SANITY_DATASET = 'production'
          await updateWhatsAppNumber()
          break
        default:
          console.log('❌ Invalid choice. Cancelled.')
      }
      resolve()
    })
  })
}

// Main execution
console.log('📱 WhatsApp Business Number Updater')
console.log('===================================')

chooseDataset().then(() => {
  process.exit(0)
}).catch((error) => {
  console.error('❌ Unexpected error:', error)
  process.exit(1)
}) 