#!/usr/bin/env tsx

import { createClient } from '@supabase/supabase-js'
import { readFileSync } from 'fs'
import { join } from 'path'

// Environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables')
  console.error('Required: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
})

async function runMigrations() {
  console.log('🗄️ Starting database migration to production...')
  
  try {
    // Test connection first
    console.log('🔍 Testing connection...')
    const { data: healthCheck, error: healthError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .limit(1)
    
    if (healthError) {
      console.error('❌ Connection failed:', healthError.message)
      return
    }
    
    console.log('✅ Connection successful')
    
    // Read and execute migrations in order
    const migrations = [
      '20250622000001_create_vehicles_tables.sql',
      '20250623000003_fix_schema_mismatch.sql',
    ]
    
    for (const migrationFile of migrations) {
      console.log(`\n📄 Running migration: ${migrationFile}`)
      
      const migrationPath = join(process.cwd(), 'supabase', 'migrations', migrationFile)
      const migrationSQL = readFileSync(migrationPath, 'utf-8')
      
      // Split into individual statements
      const statements = migrationSQL
        .split(';')
        .map(s => s.trim())
        .filter(s => s.length > 0 && !s.startsWith('--'))
        .map(s => s + ';')
      
      console.log(`   Found ${statements.length} SQL statements`)
      
      for (let i = 0; i < statements.length; i++) {
        const statement = statements[i]
        
        if (statement.trim() === ';') continue
        
        try {
          // Use rpc to execute raw SQL
          const { error } = await supabase.rpc('exec_sql', { 
            sql: statement 
          })
          
          if (error) {
            // Some statements might fail if already exist, that's ok
            if (error.message.includes('already exists') || 
                error.message.includes('does not exist') ||
                error.message.includes('permission denied')) {
              console.log(`   ⚠️  Statement ${i + 1}: ${error.message}`)
            } else {
              console.error(`   ❌ Statement ${i + 1} failed:`, error.message)
            }
          } else {
            console.log(`   ✅ Statement ${i + 1} executed`)
          }
        } catch (err: any) {
          console.log(`   ⚠️  Statement ${i + 1}: ${err.message}`)
        }
      }
      
      console.log(`✅ Migration ${migrationFile} completed`)
    }
    
    // Verify tables exist
    console.log('\n🔍 Verifying migration results...')
    
    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .in('table_name', ['vehicles', 'stages'])
    
    if (tablesError) {
      console.error('❌ Could not verify tables:', tablesError.message)
    } else {
      console.log('📊 Tables found:', tables?.map(t => t.table_name))
    }
    
    // Test a simple query
    const { data: vehicleCount, error: countError } = await supabase
      .from('vehicles')
      .select('*', { count: 'exact', head: true })
    
    if (countError) {
      console.error('❌ Could not query vehicles table:', countError.message)
    } else {
      console.log(`🚗 Current vehicles in database: ${vehicleCount || 0}`)
    }
    
    console.log('\n🎉 Migration completed successfully!')
    console.log('\n📝 Next steps:')
    console.log('   1. Run: pnpm tsx scripts/import-sma-data.ts')
    console.log('   2. Verify data import')
    console.log('   3. Test configurator API')
    
  } catch (error: any) {
    console.error('❌ Migration failed:', error.message)
    process.exit(1)
  }
}

// Execute if called directly
if (require.main === module) {
  runMigrations()
} 