import { createClient } from '@sanity/client'

// Environment variables
const projectId = process.env.NEXT_PUBLIC_SANITY_PROJECT_ID
const token = process.env.SANITY_API_READ_TOKEN

if (!projectId || !token) {
  throw new Error('Missing Sanity environment variables')
}

// Create clients for both datasets
const devClient = createClient({
  projectId,
  dataset: 'development',
  apiVersion: '2024-01-01',
  token,
  useCdn: false,
})

const prodClient = createClient({
  projectId,
  dataset: 'production',
  apiVersion: '2024-01-01',
  token,
  useCdn: false,
})

const detailingPageData = {
  _type: 'detailingPage',
  title: 'Fahrzeugaufbereitung',
  heroSection: {
    heroTitle: 'Professionelle Fahrzeugaufbereitung',
    heroSubtitle: 'Perfekte Pflege für Ihr Fahrzeug',
    heroDescription: 'Wir bieten professionelle Fahrzeugaufbereitung mit modernsten Techniken und höchstwertigen Produkten. Von der Lackpflege bis zur Innenraumaufbereitung - Ihr Fahrzeug erstrahlt in neuem Glanz.',
  },
  services: [
    {
      _key: 'polishing',
      title: 'Lackpolitur & Aufbereitung',
      description: 'Professionelle Lackpolitur mit modernsten Techniken und Produkten. Wir entfernen Kratzer, Swirl-Marks und bringen Ihren Lack zum Strahlen.',
      features: [
        'Mehrstufige Lackpolitur',
        'Entfernung von Kratzern und Swirl-Marks',
        'Hochwertige Politurprodukte',
        'Professionelle Lackbewertung',
        'Schutzversiegelung inklusive'
      ],
      buttonText: 'Termin vereinbaren',
      buttonLink: 'mailto:<EMAIL>',
      isExternalLink: true,
      color: 'blue',
      icon: 'polish',
      order: 1,
    },
    {
      _key: 'ceramic',
      title: 'Keramikversiegelung',
      description: 'Langanhaltender Schutz durch professionelle Keramikversiegelung. Schützt vor Umwelteinflüssen und sorgt für dauerhaften Glanz.',
      features: [
        'Premium Keramikbeschichtung',
        'Bis zu 2 Jahre Schutz',
        'Wasser- und schmutzabweisend',
        'UV-Schutz inklusive',
        'Einfache Reinigung'
      ],
      buttonText: 'Beratung anfragen',
      buttonLink: 'mailto:<EMAIL>',
      isExternalLink: true,
      color: 'purple',
      icon: 'shield',
      order: 2,
    },
    {
      _key: 'wax',
      title: 'Premium Wachsversiegelung',
      description: 'Traditionelle Wachsversiegelung mit hochwertigen Produkten für tiefen Glanz und natürlichen Schutz Ihres Fahrzeugs.',
      features: [
        'Premium Carnauba-Wachs',
        'Tiefenglanz und Spiegeleffekt',
        'Natürlicher Lackschutz',
        'Regelmäßige Auffrischung möglich',
        'Klassische Handwerkskunst'
      ],
      buttonText: 'Angebot anfordern',
      buttonLink: 'mailto:<EMAIL>',
      isExternalLink: true,
      color: 'orange',
      icon: 'sparkles',
      order: 3,
    },
    {
      _key: 'interior',
      title: 'Innenraumaufbereitung',
      description: 'Professionelle Reinigung und Pflege Ihres Fahrzeuginnenraums. Von der Polsterreinigung bis zur Lederpflege.',
      features: [
        'Polster- und Teppichreinigung',
        'Professionelle Lederpflege',
        'Armaturenbrett-Aufbereitung',
        'Geruchsentfernung',
        'Antibakterielle Behandlung'
      ],
      buttonText: 'Termin buchen',
      buttonLink: 'mailto:<EMAIL>',
      isExternalLink: true,
      color: 'teal',
      icon: 'car',
      order: 4,
    },
  ],
  ctaSection: {
    ctaTitle: 'Bereit für eine professionelle Aufbereitung?',
    ctaDescription: 'Kontaktieren Sie uns für ein unverbindliches Angebot. Wir beraten Sie gerne über die beste Aufbereitungslösung für Ihr Fahrzeug.',
    ctaButtonText: 'Jetzt Termin vereinbaren',
  },
  lastUpdated: new Date().toISOString(),
}

export const seedDetailingPage = async () => {
  try {
    console.log('🌱 Seeding Detailing Page data...')

    // Seed development dataset
    console.log('📝 Creating detailing page in development dataset...')
    const devResult = await devClient.createOrReplace({
      _id: 'detailing-page',
      ...detailingPageData,
    })
    console.log('✅ Development dataset seeded:', devResult._id)

    // Seed production dataset
    console.log('📝 Creating detailing page in production dataset...')
    const prodResult = await prodClient.createOrReplace({
      _id: 'detailing-page',
      ...detailingPageData,
    })
    console.log('✅ Production dataset seeded:', prodResult._id)

    console.log('🎉 Detailing page seeding completed successfully!')
  } catch (error) {
    console.error('❌ Error seeding detailing page:', error)
    throw error
  }
}

// Run if called directly
seedDetailingPage(); 