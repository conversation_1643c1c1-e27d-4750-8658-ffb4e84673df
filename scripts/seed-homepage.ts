import { createClient } from 'next-sanity';

// Load environment variables
import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

// Direct client configuration
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID!,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET!,
  apiVersion: process.env.NEXT_PUBLIC_SANITY_API_VERSION || '2024-01-01',
  useCdn: false,
  token: process.env.SANITY_API_READ_TOKEN,
});

const homepageData = {
  _type: 'homepage',
  heroTitle: 'Professionelle Fahrzeugoptimierung auf höchstem Niveau',
  heroSubtitle: 'Maximieren Sie die Leistung Ihres Fahrzeugs mit unserem Chiptuning-Service',
  heroDescription: 'Entdecken Sie das versteckte Potenzial Ihres Motors durch professionelle Software-Optimierung. Mehr Le<PERSON>ung, besserer Kraftstoffverbrauch und optimiertes Fahrverhalten.',
  ctaButtonText: 'Konfigurator starten',
  ctaSecondaryText: 'Unsere Services',
  features: [
    {
      _type: 'featureCard',
      _key: 'performance',
      title: 'Leistungssteigerung',
      description: 'Bis zu 30% mehr Leistung durch professionelle Software-Optimierung',
      icon: 'lightning',
      order: 1,
    },
    {
      _type: 'featureCard',
      _key: 'quality',
      title: 'Qualitätsgarantie',
      description: 'Zertifizierte Techniker und modernste Ausrüstung für beste Ergebnisse',
      icon: 'check',
      order: 2,
    },
    {
      _type: 'featureCard',
      _key: 'service',
      title: 'Schneller Service',
      description: 'Terminvereinbarung innerhalb von 24 Stunden möglich',
      icon: 'clock',
      order: 3,
    },
  ],
};

async function seedHomepage() {
  try {
    console.log('🏠 Seeding homepage data...');
    
    // Delete existing homepage to recreate with proper _key values
    const existing = await client.fetch('*[_type == "homepage"][0]');
    
    if (existing) {
      console.log('🗑️ Deleting existing homepage...');
      await client.delete(existing._id);
      console.log('✅ Existing homepage deleted');
    }
    
    console.log('📝 Creating new homepage with proper _key values...');
    const result = await client.create(homepageData);
    console.log('✅ Homepage created:', result._id);
    
    console.log('🎉 Homepage seeding completed!');
  } catch (error) {
    console.error('❌ Error seeding homepage:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  seedHomepage();
}

export { seedHomepage }; 