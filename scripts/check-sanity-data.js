import { createClient } from '@sanity/client'

const projectId = 'uzktk1gg'
const adminToken = 'skkr72eqIhavsdBJHo4bW4tgRcgD0PJQ0LYieeR5ScPe3yiOPE5jCDXFke9w09FdAVItL43v1Z14CsSoq'
const datasets = ['development', 'production']

async function checkSanityData() {
  for (const dataset of datasets) {
    console.log(`\n📊 Checking ${dataset} dataset...`)

    const client = createClient({
      projectId,
      dataset,
      token: adminToken,
      useCdn: false,
      apiVersion: '2024-01-01'
    })

    try {
      // Check all document types
      const allDocs = await client.fetch('*[0...10] { _type, _id, title }')
      console.log(`📄 Found ${allDocs.length} documents:`)
      allDocs.forEach(doc => {
        console.log(`  - ${doc._type}: ${doc._id} (${doc.title || 'No title'})`)
      })

      // Specifically look for site settings
      const siteSettings = await client.fetch('*[_type == "siteSettings"]')
      console.log(`⚙️  Site settings documents: ${siteSettings.length}`)
      
      if (siteSettings.length > 0) {
        console.log(`📞 Current phone in footer:`, siteSettings[0].footer?.phone || 'Not set')
      }
      
    } catch (error) {
      console.error(`❌ Error checking ${dataset} dataset:`, error.message)
    }
  }
}

checkSanityData().catch(console.error)
