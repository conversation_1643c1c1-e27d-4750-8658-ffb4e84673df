import { createClient } from 'next-sanity';

// Load environment variables
import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

// Direct client configuration
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID!,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET!,
  apiVersion: process.env.NEXT_PUBLIC_SANITY_API_VERSION || '2024-01-01',
  useCdn: false,
  token: process.env.SANITY_API_READ_TOKEN,
});

const legalPages = [
  {
    _type: 'legalPage',
    title: 'Impressum',
    slug: { current: 'impressum' },
    content: [
      {
        _type: 'block',
        _key: 'impressum1',
        children: [
          {
            _type: 'span',
            _key: 'span1',
            text: 'DB-Performance Garage Bytyci',
            marks: ['strong']
          }
        ],
        markDefs: [],
        style: 'h2'
      },
      {
        _type: 'block',
        _key: 'impressum2',
        children: [
          {
            _type: 'span',
            _key: 'span2',
            text: 'Geschäftsführer: [Name]'
          }
        ],
        markDefs: [],
        style: 'normal'
      },
      {
        _type: 'block',
        _key: 'impressum3',
        children: [
          {
            _type: 'span',
            _key: 'span3',
            text: 'Adresse:\nMusterstrasse 123\n8000 Zürich\nSchweiz'
          }
        ],
        markDefs: [],
        style: 'normal'
      },
      {
        _type: 'block',
        _key: 'impressum4',
        children: [
          {
            _type: 'span',
            _key: 'span4',
            text: 'E-Mail: <EMAIL>\nTelefon: +41 44 123 45 67'
          }
        ],
        markDefs: [],
        style: 'normal'
      },
      {
        _type: 'block',
        _key: 'impressum5',
        children: [
          {
            _type: 'span',
            _key: 'span5',
            text: 'Handelsregister: [CHE-Nummer]'
          }
        ],
        markDefs: [],
        style: 'normal'
      }
    ],
    lastUpdated: new Date().toISOString(),
  },
  {
    _type: 'legalPage',
    title: 'Datenschutzerklärung',
    slug: { current: 'datenschutz' },
    content: [
      {
        _type: 'block',
        _key: 'privacy1',
        children: [
          {
            _type: 'span',
            _key: 'span1',
            text: 'Datenschutzerklärung',
            marks: ['strong']
          }
        ],
        markDefs: [],
        style: 'h2'
      },
      {
        _type: 'block',
        _key: 'privacy2',
        children: [
          {
            _type: 'span',
            _key: 'span2',
            text: 'Der Schutz Ihrer persönlichen Daten ist uns ein besonderes Anliegen. Wir verarbeiten Ihre Daten daher ausschliesslich auf Grundlage der gesetzlichen Bestimmungen (DSGVO, TKG 2003).'
          }
        ],
        markDefs: [],
        style: 'normal'
      },
      {
        _type: 'block',
        _key: 'privacy3',
        children: [
          {
            _type: 'span',
            _key: 'span3',
            text: 'Datenerfassung auf unserer Website',
            marks: ['strong']
          }
        ],
        markDefs: [],
        style: 'h3'
      },
      {
        _type: 'block',
        _key: 'privacy4',
        children: [
          {
            _type: 'span',
            _key: 'span4',
            text: 'Wenn Sie unsere Website besuchen, werden automatisch Informationen allgemeiner Natur erfasst. Diese Informationen (Server-Logfiles) beinhalten etwa die Art des Webbrowsers, das verwendete Betriebssystem, den Domainnamen Ihres Internet-Service-Providers und ähnliches.'
          }
        ],
        markDefs: [],
        style: 'normal'
      },
      {
        _type: 'block',
        _key: 'privacy5',
        children: [
          {
            _type: 'span',
            _key: 'span5',
            text: 'Kontaktformular',
            marks: ['strong']
          }
        ],
        markDefs: [],
        style: 'h3'
      },
      {
        _type: 'block',
        _key: 'privacy6',
        children: [
          {
            _type: 'span',
            _key: 'span6',
            text: 'Wenn Sie uns per Kontaktformular Anfragen zukommen lassen, werden Ihre Angaben aus dem Anfrageformular inklusive der von Ihnen dort angegebenen Kontaktdaten zwecks Bearbeitung der Anfrage und für den Fall von Anschlussfragen bei uns gespeichert.'
          }
        ],
        markDefs: [],
        style: 'normal'
      }
    ],
    lastUpdated: new Date().toISOString(),
  },
  {
    _type: 'legalPage',
    title: 'Allgemeine Geschäftsbedingungen',
    slug: { current: 'agb' },
    content: [
      {
        _type: 'block',
        _key: 'agb1',
        children: [
          {
            _type: 'span',
            _key: 'span1',
            text: 'Allgemeine Geschäftsbedingungen',
            marks: ['strong']
          }
        ],
        markDefs: [],
        style: 'h2'
      },
      {
        _type: 'block',
        _key: 'agb2',
        children: [
          {
            _type: 'span',
            _key: 'span2',
            text: '1. Geltungsbereich',
            marks: ['strong']
          }
        ],
        markDefs: [],
        style: 'h3'
      },
      {
        _type: 'block',
        _key: 'agb3',
        children: [
          {
            _type: 'span',
            _key: 'span3',
            text: 'Diese Allgemeinen Geschäftsbedingungen gelten für alle Verträge über die Lieferung von Waren und Dienstleistungen, die zwischen DB-Performance und dem Kunden geschlossen werden.'
          }
        ],
        markDefs: [],
        style: 'normal'
      },
      {
        _type: 'block',
        _key: 'agb4',
        children: [
          {
            _type: 'span',
            _key: 'span4',
            text: '2. Tuning-Dienstleistungen',
            marks: ['strong']
          }
        ],
        markDefs: [],
        style: 'h3'
      },
      {
        _type: 'block',
        _key: 'agb5',
        children: [
          {
            _type: 'span',
            _key: 'span5',
            text: 'Alle Tuning-Massnahmen werden ausschliesslich für den Motorsport oder den Export ausserhalb der EU angeboten. Der Betrieb getunter Fahrzeuge im öffentlichen Strassenverkehr kann zu einem Erlöschen der Betriebserlaubnis führen.'
          }
        ],
        markDefs: [],
        style: 'normal'
      },
      {
        _type: 'block',
        _key: 'agb6',
        children: [
          {
            _type: 'span',
            _key: 'span6',
            text: '3. Gewährleistung',
            marks: ['strong']
          }
        ],
        markDefs: [],
        style: 'h3'
      },
      {
        _type: 'block',
        _key: 'agb7',
        children: [
          {
            _type: 'span',
            _key: 'span7',
            text: 'Wir gewährleisten für unsere Leistungen nach den gesetzlichen Bestimmungen. Die Gewährleistungsfrist beträgt 12 Monate ab Leistungserbringung.'
          }
        ],
        markDefs: [],
        style: 'normal'
      }
    ],
    lastUpdated: new Date().toISOString(),
  }
];

async function seedLegalPages() {
  try {
    console.log('⚖️ Seeding legal pages...');
    
    // Delete existing legal pages
    const existing = await client.fetch('*[_type == "legalPage"]');
    for (const page of existing) {
      await client.delete(page._id);
      console.log(`🗑️ Deleted: ${page.title}`);
    }
    
    // Create new legal pages
    for (const page of legalPages) {
      const result = await client.create(page);
      console.log(`✅ Created: ${page.title} (${result._id})`);
    }
    
    console.log('🎉 Legal pages seeding completed!');
    console.log('📋 Created pages:');
    console.log('   - /impressum - Impressum');
    console.log('   - /datenschutz - Datenschutzerklärung');
    console.log('   - /agb - Allgemeine Geschäftsbedingungen');
    
  } catch (error) {
    console.error('❌ Error seeding legal pages:', error);
    process.exit(1);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  seedLegalPages();
}

export { seedLegalPages }; 