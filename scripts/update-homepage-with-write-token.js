import { createClient } from '@sanity/client'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config({ path: '.env.local' })

const projectId = process.env.NEXT_PUBLIC_SANITY_PROJECT_ID

if (!projectId) {
  console.error('Missing NEXT_PUBLIC_SANITY_PROJECT_ID')
  process.exit(1)
}

// Use the CLI auth token from sanity debug --secrets
const writeToken = process.env.SANITY_WRITE_TOKEN || '*********************************************************************************'

if (!writeToken) {
  console.log('🔑 No write token found!')
  console.log('Run: npx sanity debug --secrets to get your auth token')
  process.exit(1)
}

// Create clients for both datasets
const developmentClient = createClient({
  projectId,
  dataset: 'development',
  useCdn: false,
  token: writeToken,
  apiVersion: '2024-01-01',
})

const productionClient = createClient({
  projectId,
  dataset: 'production', 
  useCdn: false,
  token: writeToken,
  apiVersion: '2024-01-01',
})

const swipingServicesData = [
  {
    _type: 'swipingService',
    _key: 'tuning',
    text: 'Professionelle Fahrzeugoptimierung durch Chiptuning',
    order: 1,
  },
  {
    _type: 'swipingService',
    _key: 'werkstatt',
    text: 'Komplette Werkstatt-Services: Ölservice, Bremsen, Getriebe und Reparaturen',
    order: 2,
  },
  {
    _type: 'swipingService',
    _key: 'aufbereitung',
    text: 'Professionelle Fahrzeugaufbereitung für perfekten Glanz und Werterhalt',
    order: 3,
  },
]

async function updateHomepageData(client, datasetName) {
  try {
    console.log(`🔄 Updating homepage data in ${datasetName} dataset...`)
    
    // Get existing homepage document
    const existingHomepage = await client.fetch('*[_type == "homepage"][0]')
    
    if (!existingHomepage) {
      console.log(`❌ No homepage document found in ${datasetName}`)
      return
    }
    
    console.log(`📄 Found homepage document: ${existingHomepage._id}`)
    
    // Update existing document with swipingServices
    const result = await client
      .patch(existingHomepage._id)
      .set({ swipingServices: swipingServicesData })
      .commit()
    
    console.log(`✅ Updated homepage document in ${datasetName}:`, result._id)
    console.log(`📝 Added ${swipingServicesData.length} swiping services`)
    
  } catch (error) {
    console.error(`❌ Error updating homepage data in ${datasetName}:`, error.message)
  }
}

async function main() {
  console.log('🚀 Starting homepage swiper data update...')
  console.log('📊 Services to add:', swipingServicesData.map(s => s.text).join(', '))
  
  await updateHomepageData(developmentClient, 'development')
  await updateHomepageData(productionClient, 'production')
  
  console.log('✅ Homepage swiper data update completed!')
  console.log('🌐 You can now test the website to see the swiping text effect')
}

main().catch(console.error)
