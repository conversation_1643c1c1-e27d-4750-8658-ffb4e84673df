#!/usr/bin/env tsx

import { readFileSync, readdirSync } from 'fs'
import { join } from 'path'

interface SimplePriceData {
  price: number
  brand: string
  vehicle_type: string
}

const extractPriceFromString = (priceString: string): number | null => {
  if (!priceString) return null
  
  // Handle verschiedene Preisformate
  const patterns = [
    /UVP:\s*(\d+(?:,\d+)?)[,\-\s]*EUR/i,
    /(\d+(?:,\d+)?)[,\-\s]*EUR/i,
    /CHF\s*(\d+(?:,\d+)?)/i,
    /(\d+(?:,\d+)?)\s*CHF/i,
    /(\d+(?:,\d+)?)\s*\€/i,
  ]
  
  for (const pattern of patterns) {
    const match = priceString.match(pattern)
    if (match) {
      const priceStr = match[1].replace(',', '')
      const price = parseInt(priceStr)
      if (!isNaN(price) && price > 0 && price < 10000) { // Reasonable range
        return price
      }
    }
  }
  
  return null
}

const analyzeFile = (filePath: string): SimplePriceData[] => {
  console.log(`📄 ${filePath.split('/').pop()}`)
  
  try {
    const rawData = readFileSync(filePath, 'utf8')
    const jsonData = JSON.parse(rawData)
    
    if (!Array.isArray(jsonData)) return []

    const priceData: SimplePriceData[] = []
    
    for (const vehicle of jsonData) {
      if (!vehicle.stages || !Array.isArray(vehicle.stages)) continue
      
      for (const stage of vehicle.stages) {
        const priceFields = [stage.price_uvp, stage.price].filter(Boolean)
        
        for (const priceField of priceFields) {
          const price = extractPriceFromString(priceField)
          if (price !== null) {
            priceData.push({
              price,
              brand: vehicle.brand || 'Unknown',
              vehicle_type: vehicle.vehicle_type || 'PKW',
            })
            break
          }
        }
      }
    }
    
    console.log(`  ✅ ${priceData.length} Preise`)
    return priceData
    
  } catch (error) {
    console.log(`  ❌ Fehler`)
    return []
  }
}

// Main execution
console.log('🔍 VEREINFACHTE SMA PREISANALYSE')
console.log('=================================\n')

const extractionDir = join(process.cwd(), 'complete_extraction_selenium')
const files = readdirSync(extractionDir)
  .filter(file => file.endsWith('.json'))
  .sort()

console.log(`📁 Analysiere ${files.length} JSON-Dateien...\n`)

const allPrices: SimplePriceData[] = []

for (const file of files) {
  const filePath = join(extractionDir, file)
  const priceData = analyzeFile(filePath)
  allPrices.push(...priceData)
}

if (allPrices.length === 0) {
  console.error('❌ Keine Preisdaten gefunden')
  process.exit(1)
}

// Einfache Statistiken
const prices = allPrices.map(p => p.price)
const minPrice = Math.min(...prices)
const maxPrice = Math.max(...prices)
const avgPrice = Math.round(prices.reduce((a, b) => a + b, 0) / prices.length)
const sortedPrices = [...prices].sort((a, b) => a - b)
const medianPrice = sortedPrices[Math.floor(sortedPrices.length / 2)]

console.log('\n📊 ZUSAMMENFASSUNG')
console.log('==================')
console.log(`📈 Anzahl Tuning-Stages: ${allPrices.length.toLocaleString()}`)
console.log(`💰 Preisbereich: ${minPrice} - ${maxPrice} EUR`)
console.log(`📊 Durchschnitt: ${avgPrice} EUR`)
console.log(`📊 Median: ${medianPrice} EUR`)

// Preiskategorien für Schweizer Markt (mit höheren Preisen)
const categories = {
  'Basic (unter 650 EUR)': prices.filter(p => p < 650).length,
  'Standard (650-830 EUR)': prices.filter(p => p >= 650 && p < 830).length,
  'Premium (830-1100 EUR)': prices.filter(p => p >= 830 && p < 1100).length,
  'High-End (über 1100 EUR)': prices.filter(p => p >= 1100).length,
}

console.log('\n💶 PREISKATEGORIEN')
console.log('==================')
Object.entries(categories).forEach(([cat, count]) => {
  const percentage = ((count / allPrices.length) * 100).toFixed(1)
  console.log(`${cat.padEnd(25)}: ${count.toString().padStart(4)} (${percentage}%)`)
})

// Top Marken
const brandCounts: Record<string, { count: number; avgPrice: number }> = {}
allPrices.forEach(item => {
  if (!brandCounts[item.brand]) {
    brandCounts[item.brand] = { count: 0, avgPrice: 0 }
  }
  brandCounts[item.brand].count++
})

Object.entries(brandCounts).forEach(([brand, data]) => {
  const brandPrices = allPrices.filter(p => p.brand === brand).map(p => p.price)
  data.avgPrice = Math.round(brandPrices.reduce((a, b) => a + b, 0) / brandPrices.length)
})

console.log('\n🏷️  TOP 10 MARKEN')
console.log('=================')
Object.entries(brandCounts)
  .sort(([,a], [,b]) => b.count - a.count)
  .slice(0, 10)
  .forEach(([brand, data], index) => {
    console.log(`${(index + 1).toString().padStart(2)}. ${brand.padEnd(15)}: ${data.count.toString().padStart(3)} Stages | ⌀ ${data.avgPrice} EUR`)
  })

// Fahrzeugtypen
const typeCounts: Record<string, number> = {}
allPrices.forEach(item => {
  typeCounts[item.vehicle_type] = (typeCounts[item.vehicle_type] || 0) + 1
})

console.log('\n🚗 FAHRZEUGTYPEN')
console.log('================')
Object.entries(typeCounts)
  .sort(([,a], [,b]) => b - a)
  .forEach(([type, count]) => {
    const percentage = ((count / allPrices.length) * 100).toFixed(1)
    console.log(`${type.padEnd(20)}: ${count.toString().padStart(4)} (${percentage}%)`)
  })

console.log('\n🎯 EMPFOHLENE PREISE FÜR CHF-UMSTELLUNG')
console.log('=======================================')
console.log(`💡 Günstigste Kategorie: Ab ${Math.round(minPrice * 1.15)} CHF`)
console.log(`📊 Durchschnittspreis: Ab ${Math.round(avgPrice * 1.15)} CHF`)
console.log(`📈 Premium-Kategorie: Ab ${Math.round(maxPrice * 1.15)} CHF`)

console.log('\n✨ VEREINFACHTE PREISKATEGORIEN FÜR FRONTEND')
console.log('============================================')
console.log(`🟢 Basic Tuning: Ab 700 CHF (${categories['Basic (unter 650 EUR)']} Optionen)`)
console.log(`🟡 Standard Tuning: Ab 900 CHF (${categories['Standard (650-830 EUR)']} Optionen)`)
console.log(`🟠 Premium Tuning: Ab 1'200 CHF (${categories['Premium (830-1100 EUR)']} Optionen)`)
console.log(`🔴 High-End Tuning: Ab 1'500 CHF (${categories['High-End (über 1100 EUR)']} Optionen)`)

console.log(`\n🎉 Analyse abgeschlossen! ${allPrices.length} Preise analysiert`) 