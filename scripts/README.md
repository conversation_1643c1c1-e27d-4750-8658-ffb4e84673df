# Vehicle Data Import Scripts

Diese Scripts verwalten den Import der extrahierten SMA-Fahrzeugdaten in die Supabase Datenbank.

## Setup

1. **Environment Variables** in `.env.local` setzen:
   ```bash
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
   ```

2. **Dependencies installieren**:
   ```bash
   pnpm install
   ```

3. **Database Migration ausführen**:
   ```bash
   supabase db push
   # oder direkt:
   psql -f supabase/migrations/20250622000001_create_vehicles_tables.sql
   ```

## Import Scripts

### `import-vehicles.ts`

Importiert alle JSON-Dateien aus `complete_extraction_selenium/` in die Datenbank.

#### Features:
- ✅ **Duplikat-Check**: Prüft Fahrzeuge anhand von `vehicle_type|brand|model|year|engine`
- ✅ **Batch Processing**: Importiert in 100er-Batches für Performance
- ✅ **Data Validation**: Validiert JSON-Schema mit Zod
- ✅ **Statistics**: Detaillierte Import-Statistiken
- ✅ **Error Handling**: Robuste Fehlerbehandlung
- ✅ **Progress Tracking**: Live-Progress während Import

#### Usage:

```bash
# Vollständiger Import
pnpm import

# Import-Check (dry-run)
pnpm import:check
```

#### Was wird importiert:

1. **Vehicles Table**:
   - `vehicle_type` (z.B. "Kraftfahrzeuge (PKW)")
   - `brand` (z.B. "BMW")
   - `model` (z.B. "3er")
   - `year` (z.B. "2020 -> 2024")
   - `engine` (z.B. "320d /190PS /Diesel")

2. **Stages Table**:
   - `name` (z.B. "Stage 1")
   - `original_power` (PS als Integer)
   - `tuned_power` (PS als Integer)
   - `price_chf` (Preis als Integer)
   - `description` (Zusammenfassung)

#### Duplikat-Logik:

Ein Fahrzeug gilt als **Duplikat**, wenn ein identischer Eintrag bereits existiert mit:
- Gleicher `vehicle_type`
- Gleicher `brand`
- Gleiches `model`
- Gleiches `year`
- Gleicher `engine`

### Import-Statistiken

Nach dem Import werden detaillierte Statistiken angezeigt:

```
📊 IMPORT STATISTICS
============================
📄 Files processed: 20
📖 Vehicles read: 12276
⏭️ Vehicles skipped: 45
🔄 Duplicates found: 1200
✅ Vehicles inserted: 11031
🎯 Stages inserted: 25678
❌ Errors: 0
```

## Database Schema

### Vehicles Table
```sql
- id: UUID (Primary Key)
- vehicle_type: TEXT (z.B. "PKW", "Motorräder")
- brand: TEXT (z.B. "BMW", "Audi")
- model: TEXT (z.B. "3er", "A4")
- year: TEXT (z.B. "2020 -> 2024")
- engine: TEXT (z.B. "320d /190PS /Diesel")
- created_at: TIMESTAMP
- updated_at: TIMESTAMP
```

### Stages Table
```sql
- id: UUID (Primary Key)
- vehicle_id: UUID (Foreign Key)
- name: TEXT (z.B. "Stage 1")
- original_power: INTEGER (PS)
- tuned_power: INTEGER (PS)
- price_chf: INTEGER (CHF)
- description: TEXT
- created_at: TIMESTAMP
- updated_at: TIMESTAMP
```

### RPC Functions

Für die Filter-Dropdowns im Konfigurator:

- `get_distinct_vehicle_types()`: Alle Fahrzeugtypen
- `get_distinct_brands(vehicle_type)`: Marken für Typ
- `get_distinct_years(vehicle_type, brand)`: Jahre für Typ+Marke
- `get_distinct_engines(vehicle_type, brand, year)`: Motoren für Typ+Marke+Jahr

## Troubleshooting

### Häufige Fehler:

1. **Database connection failed**:
   - Prüfe Supabase URL und Service Key
   - Stelle sicher, dass Supabase-Projekt läuft

2. **Migration not found**:
   ```bash
   # Migration manuell ausführen:
   supabase db push
   ```

3. **Permission denied**:
   - Verwende `SUPABASE_SERVICE_ROLE_KEY` (nicht den anon key)
   - Prüfe RLS Policies

4. **JSON parsing errors**:
   - Prüfe ob JSON-Dateien korrumpiert sind
   - Script überspringt invalide Dateien automatisch

### Performance:

- **Batch Size**: 100 Fahrzeuge pro Batch (anpassbar)
- **Rate Limiting**: 100ms Pause zwischen Batches
- **Memory**: Prozessiert Dateien einzeln (kein OOM)
- **Indexes**: Optimierte DB-Indexes für alle Filter

## Erweiterungen

### Neue Datenquellen hinzufügen:

1. JSON-Schema in `VehicleSchema` erweitern
2. Parser-Funktionen in `extractPowerValue()` etc. anpassen
3. Duplikat-Check in `createVehicleHash()` erweitern

### Custom Import-Logik:

Das Script kann als Modul importiert werden:

```typescript
import { importVehicles } from './scripts/import-vehicles'

await importVehicles()
``` 