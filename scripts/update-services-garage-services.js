import { createClient } from '@sanity/client'

const projectId = 'uzktk1gg'
const adminToken = 'skkr72eqIhavsdBJHo4bW4tgRcgD0PJQ0LYieeR5ScPe3yiOPE5jCDXFke9w09FdAVItL43v1Z14CsSoq'

// New garage services to replace "Beratung & Service"
const garageServicesCard = {
  _type: 'serviceCard',
  _key: 'garage-services',
  title: 'Garagen-Service',
  description: 'Komplette Werkstattdienstleistungen: Ölservice, Getriebeservice, Bremsen, Inspektion und allgemeine Reparaturen.',
  features: ['Ölwechsel & Filter', 'Bremsenservice', 'Getriebeservice', 'Inspektion & Wartung', 'Allgemeine Reparaturen', 'Klimaanlagen-Service'],
  buttonText: 'Termin vereinbaren',
  buttonLink: '/contact',
  isExternalLink: false,
  color: 'teal',
  icon: 'cog',
  order: 6,
}

async function updateServicesPageWithGarageServices() {
  const datasets = ['development', 'production']
  
  for (const dataset of datasets) {
    console.log(`\n🔧 Updating services page in ${dataset} dataset...`)
    
    const client = createClient({
      projectId,
      dataset,
      token: adminToken,
      useCdn: false,
      apiVersion: '2024-01-01'
    })

    try {
      // Get existing services page
      const servicesPage = await client.fetch('*[_type == "servicesPage"][0]')
      
      if (!servicesPage) {
        console.log(`❌ No services page found in ${dataset}`)
        continue
      }

      console.log(`📋 Current services page ID: ${servicesPage._id}`)
      console.log(`📊 Current services count: ${servicesPage.services?.length || 0}`)
      
      // Find and replace "Beratung & Service" with "Garagen-Service"
      const updatedServices = servicesPage.services?.map(service => {
        if (service._key === 'beratung-service' || service.title === 'Beratung & Service') {
          console.log(`🔄 Replacing "${service.title}" with "Garagen-Service"`)
          return garageServicesCard
        }
        return service
      }) || []

      // If no "Beratung & Service" found, add garage services anyway
      if (!updatedServices.find(s => s._key === 'garage-services')) {
        console.log(`➕ Adding "Garagen-Service" as new service`)
        updatedServices.push(garageServicesCard)
      }

      // Update the services page
      const result = await client
        .patch(servicesPage._id)
        .set({
          services: updatedServices
        })
        .commit()

      console.log(`✅ Successfully updated services page in ${dataset} dataset`)
      console.log(`📄 Updated services count: ${updatedServices.length}`)
      
      // Show what was updated
      updatedServices.forEach((service, index) => {
        console.log(`   ${index + 1}. ${service.title} (${service._key})`)
      })
      
    } catch (error) {
      console.error(`❌ Error updating ${dataset}:`, error.message)
    }
  }
  
  console.log('\n🎉 Services page update completed!')
  console.log('\n📝 Summary:')
  console.log('✅ "Beratung & Service" replaced with "Garagen-Service"')
  console.log('✅ Added comprehensive garage services features')
  console.log('✅ Updated both development and production datasets')
}

updateServicesPageWithGarageServices().catch(console.error)
