const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function checkData() {
  try {
    const { count: vehicleCount } = await supabase
      .from('vehicles')
      .select('*', { count: 'exact', head: true });
      
    const { count: stageCount } = await supabase
      .from('stages')
      .select('*', { count: 'exact', head: true });
    
    console.log('🎉 IMPORT ERFOLGREICH!');
    console.log('🚗 Fahrzeuge in der Datenbank:', vehicleCount);
    console.log('⚡ Tuning-Stages in der Datenbank:', stageCount);
    
    // Ein paar Beispiel-Fahrzeuge anzeigen
    const { data: sampleVehicles } = await supabase
      .from('vehicles')
      .select('brand, model, engine')
      .limit(5);
      
    console.log('\n📋 Beispiel-Fahrzeuge:');
    sampleVehicles?.forEach(v => 
      console.log(`- ${v.brand} ${v.model} (${v.engine})`)
    );
    
    // Beispiel-Stages anzeigen
    const { data: sampleStages } = await supabase
      .from('stages')
      .select('name, original_power, tuned_power, price_chf')
      .limit(3);
      
    console.log('\n⚡ Beispiel-Tuning-Stages:');
    sampleStages?.forEach(s => 
      console.log(`- ${s.name}: ${s.original_power} → ${s.tuned_power} PS (CHF ${s.price_chf})`)
    );
    
  } catch (error) {
    console.error('❌ Fehler beim Prüfen:', error);
  }
}

checkData(); 