const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
);

async function runMigration() {
  try {
    console.log('🔄 Reading migration file...');
    
    const migrationPath = path.join(__dirname, '../supabase/migrations/20240101000000_initial_schema.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf-8');
    
    console.log('🗄️ Executing database migration...');
    
    // Split SQL into individual statements
    const statements = migrationSQL
      .split(';')
      .filter(statement => statement.trim())
      .map(statement => statement.trim() + ';');
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      
      if (statement.trim() === ';') continue;
      
      console.log(`Executing statement ${i + 1}/${statements.length}...`);
      
      const { error } = await supabase.rpc('exec_sql', { 
        sql: statement 
      });
      
      if (error) {
        // Try direct SQL execution if RPC doesn't work
        const { error: directError } = await supabase
          .from('_')
          .select()
          .eq('sql', statement);
          
        if (directError) {
          console.log(`Statement ${i + 1}: ${statement.substring(0, 100)}...`);
          console.log('Error (might be expected for policy creation):', error.message);
        }
      } else {
        console.log(`✅ Statement ${i + 1} executed successfully`);
      }
    }
    
    console.log('\n🎉 Migration completed! Testing table creation...');
    
    // Test if tables were created
    const { data: vehiclesTest, error: vehiclesError } = await supabase
      .from('vehicles')
      .select('id')
      .limit(1);
      
    if (vehiclesError) {
      console.log('❌ Vehicles table check failed:', vehiclesError.message);
    } else {
      console.log('✅ Vehicles table exists');
    }
    
    const { data: stagesTest, error: stagesError } = await supabase
      .from('stages')
      .select('id')
      .limit(1);
      
    if (stagesError) {
      console.log('❌ Stages table check failed:', stagesError.message);
    } else {
      console.log('✅ Stages table exists');
    }
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
  }
}

console.log('🚀 Starting database migration...');
runMigration(); 