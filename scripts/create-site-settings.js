import { createClient } from '@sanity/client'

const projectId = 'uzktk1gg'
const datasets = ['development', 'production']

const siteSettingsData = {
  _type: 'siteSettings',
  _id: 'siteSettings',
  siteName: 'DB-Performance Garage Bytyci',
  logo: null,
  navigation: {
    homeLabel: 'Start',
    servicesLabel: 'Dienstleistungen',
    tuningLabel: 'Tuning-Konfigurator',
    detailingLabel: 'Fahrzeugaufbereitung',
    contactLabel: 'Kontakt'
  },
  footer: {
    companyName: 'DB-Performance Garage Bytyci',
    address: 'Stauseestrasse 1, 5316 Leuggern, Schweiz',
    rightsText: 'Alle Rechte vorbehalten',
    legalSectionTitle: 'Rechtliches',
    contactSectionTitle: 'Kontakt',
    phone: '+41 76 250 07 61',
    email: '<EMAIL>',
    legalLinks: {
      imprintText: 'Impressum',
      privacyText: 'Datenschutz',
      termsText: 'AGB'
    }
  }
}

async function createSiteSettings() {
  for (const dataset of datasets) {
    console.log(`\n🔧 Creating site settings in ${dataset} dataset...`)
    
    const client = createClient({
      projectId,
      dataset,
      useCdn: false,
      apiVersion: '2024-01-01'
    })

    try {
      // Create or replace the site settings document
      const result = await client.createOrReplace(siteSettingsData)
      
      console.log(`✅ Successfully created site settings in ${dataset} dataset`)
      console.log(`📄 Document ID: ${result._id}`)
      console.log(`📞 Phone number set to: ${result.footer.phone}`)
      
    } catch (error) {
      console.error(`❌ Error creating site settings in ${dataset} dataset:`, error.message)
    }
  }
  
  console.log('\n🎉 Site settings creation completed!')
}

createSiteSettings().catch(console.error)
