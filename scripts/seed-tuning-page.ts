import { createClient } from 'next-sanity';

// Load environment variables
import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

// Direct client configuration
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID!,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET!,
  apiVersion: process.env.NEXT_PUBLIC_SANITY_API_VERSION || '2024-01-01',
  useCdn: false,
  token: process.env.SANITY_API_READ_TOKEN,
});

const tuningPageData = {
  _type: 'tuningPage',
  pageTitle: 'Tuning-Konfigurator',
  pageSubtitle: 'Finden Sie die perfekte Tuning-Lösung für Ihr Fahrzeug',
  configuratorSettings: {
    loadingText: 'Lade Konfigurator...',
    introText: 'Wählen Sie Ihr Fahrzeug aus und entdecken Sie die verfügbaren Tuning-Optionen.',
    defaultVehicleType: 'Kraftfahrzeuge (PKW)',
    pricePrefix: 'Ab',
    priceSuffix: 'CHF',
    priceIncludesTax: 'inkl. MwSt.',
    minSpecsDisclaimer: 'Mindestangaben',
  },
  labels: {
    vehicleTypeLabel: 'Fahrzeugtyp',
    brandLabel: 'Marke',
    modelLabel: 'Modell',
    yearLabel: 'Baujahr',
    motorLabel: 'Motor',
    selectPlaceholder: 'Wählen Sie...',
    performanceTitle: 'Leistungssteigerung',
    originalLabel: 'Original',
    tunedLabel: 'Getunt',
    differenceLabel: 'Unterschied',
  },
};

async function seedTuningPage() {
  try {
    console.log('🏎️ Seeding tuning page data...');
    
    // Delete existing tuning page to recreate
    const existing = await client.fetch('*[_type == "tuningPage"][0]');
    
    if (existing) {
      console.log('🗑️ Deleting existing tuning page...');
      await client.delete(existing._id);
      console.log('✅ Existing tuning page deleted');
    }
    
    console.log('📝 Creating new tuning page...');
    const result = await client.create(tuningPageData);
    console.log('✅ Tuning page created:', result._id);
    
    console.log('🎉 Tuning page seeding completed!');
  } catch (error) {
    console.error('❌ Error seeding tuning page:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  seedTuningPage();
}

export { seedTuningPage }; 