import { createClient } from '@sanity/client'

const projectId = 'uzktk1gg'
const adminToken = 'skkr72eqIhavsdBJHo4bW4tgRcgD0PJQ0LYieeR5ScPe3yiOPE5jCDXFke9w09FdAVItL43v1Z14CsSoq'

// New garage services feature to replace "Qualitätsgarantie"
const garageServicesFeature = {
  _type: 'featureCard',
  _key: 'garage-services',
  title: 'Garagen-Service',
  description: 'Komplette Werkstattdienstleistungen: Ölservice, Bremsen, Getriebe und allgemeine Reparaturen',
  icon: 'cog',
  order: 2,
}

async function updateHomepageWithGarageServices() {
  const datasets = ['development', 'production']
  
  for (const dataset of datasets) {
    console.log(`\n🏠 Updating homepage in ${dataset} dataset...`)
    
    const client = createClient({
      projectId,
      dataset,
      token: adminToken,
      useCdn: false,
      apiVersion: '2024-01-01'
    })

    try {
      // Get existing homepage
      const homepage = await client.fetch('*[_type == "homepage"][0]')
      
      if (!homepage) {
        console.log(`❌ No homepage found in ${dataset}`)
        continue
      }

      console.log(`📋 Current homepage ID: ${homepage._id}`)
      console.log(`📊 Current features count: ${homepage.features?.length || 0}`)
      
      // Find and replace "Qualitätsgarantie" with "Garagen-Service"
      const updatedFeatures = homepage.features?.map(feature => {
        if (feature._key === 'quality' || feature.title === 'Qualitätsgarantie') {
          console.log(`🔄 Replacing "${feature.title}" with "Garagen-Service"`)
          return garageServicesFeature
        }
        return feature
      }) || []

      // If no "Qualitätsgarantie" found, add garage services anyway
      if (!updatedFeatures.find(f => f._key === 'garage-services')) {
        console.log(`➕ Adding "Garagen-Service" as new feature`)
        updatedFeatures.push(garageServicesFeature)
      }

      // Update the homepage
      const result = await client
        .patch(homepage._id)
        .set({
          features: updatedFeatures
        })
        .commit()

      console.log(`✅ Successfully updated homepage in ${dataset} dataset`)
      console.log(`📄 Updated features count: ${updatedFeatures.length}`)
      
      // Show what was updated
      updatedFeatures.forEach((feature, index) => {
        console.log(`   ${index + 1}. ${feature.title} (${feature._key})`)
      })
      
    } catch (error) {
      console.error(`❌ Error updating ${dataset}:`, error.message)
    }
  }
  
  console.log('\n🎉 Homepage update completed!')
  console.log('\n📝 Summary:')
  console.log('✅ "Qualitätsgarantie" replaced with "Garagen-Service" on homepage')
  console.log('✅ Updated both development and production datasets')
  console.log('✅ Services page remains unchanged')
}

updateHomepageWithGarageServices().catch(console.error)
