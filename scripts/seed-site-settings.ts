import { createClient } from 'next-sanity';

// Load environment variables
import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

// Direct client configuration
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID!,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET!,
  apiVersion: process.env.NEXT_PUBLIC_SANITY_API_VERSION || '2024-01-01',
  useCdn: false,
  token: process.env.SANITY_API_READ_TOKEN,
});

const siteSettingsData = {
  _type: 'siteSettings',
  siteName: 'DB-Performance Garage Bytyci',
  siteDescription: 'Professionelle Fahrzeugoptimierung und Chiptuning-Service in der Schweiz',
  navigation: {
    homeLabel: 'Start',
    servicesLabel: 'Dienstleistungen',
    tuningLabel: 'Tuning-Konfigurator',
    contactLabel: 'Kontakt',
  },
  footer: {
    companyName: 'DB-Performance Garage Bytyci',
    address: 'Stauseestrasse 1, 5316 Le<PERSON>n, Schweiz',
    rightsText: 'Alle Rechte vorbehalten',
    legalSectionTitle: 'Rechtliches',
    contactSectionTitle: 'Kontakt',
    phone: '+41 XX XXX XX XX',
    email: '<EMAIL>',
    whatsapp: '+41762500761',
    legalLinks: {
      imprintText: 'Impressum',
      privacyText: 'Datenschutz',
      termsText: 'AGB',
    },
  },
};

async function seedSiteSettings() {
  try {
    console.log('⚙️ Seeding site settings data...');
    
    // Delete existing site settings to recreate
    const existing = await client.fetch('*[_type == "siteSettings"][0]');
    
    if (existing) {
      console.log('🗑️ Deleting existing site settings...');
      await client.delete(existing._id);
      console.log('✅ Existing site settings deleted');
    }
    
    console.log('📝 Creating new site settings...');
    const result = await client.create(siteSettingsData);
    console.log('✅ Site settings created:', result._id);
    
    console.log('🎉 Site settings seeding completed!');
  } catch (error) {
    console.error('❌ Error seeding site settings:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  seedSiteSettings();
}

export { seedSiteSettings }; 