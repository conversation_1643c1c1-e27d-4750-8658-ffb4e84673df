-- Reset Production Database
-- This will drop everything and recreate the schema fresh

-- Step 1: Drop everything
DROP TABLE IF EXISTS public.stages CASCADE;
DROP TABLE IF EXISTS public.vehicles CASCADE;
DROP FUNCTION IF EXISTS get_distinct_motors(text, text, text, text) CASCADE;
DROP FUNCTION IF EXISTS get_distinct_models(text, text) CASCADE;
DROP FUNCTION IF EXISTS get_distinct_brands(text) CASCADE;
DROP FUNCTION IF EXISTS get_distinct_vehicle_types() CASCADE;
DROP FUNCTION IF EXISTS get_distinct_years(text, text) CASCADE;
DROP FUNCTION IF EXISTS get_distinct_engines(text, text, text) CASCADE;
DROP FUNCTION IF EXISTS get_vehicles_with_stages(text, text, text, text, text, integer) CASCADE;
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;

-- Step 2: Enable extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Step 3: Create vehicles table with correct schema (motor instead of engine)
CREATE TABLE public.vehicles (
  id uuid default uuid_generate_v4() primary key,
  vehicle_type text not null,
  brand text not null,
  model text not null,
  year text,
  motor text not null,  -- Use motor instead of engine
  scraped_at timestamp with time zone,
  country text,
  total_stages integer,
  created_at timestamp with time zone default now() not null,
  updated_at timestamp with time zone default now() not null
);

-- Step 4: Create stages table with SMA data structure
CREATE TABLE public.stages (
  id uuid default uuid_generate_v4() primary key,
  vehicle_id uuid references public.vehicles(id) on delete cascade not null,
  name text not null,
  stage_number integer,
  raw_table_text text,
  power_original text,
  power_tuned text,
  power_difference text,
  torque_original text,
  torque_tuned text,
  torque_difference text,
  price text,
  price_uvp text,
  created_at timestamp with time zone default now() not null,
  updated_at timestamp with time zone default now() not null
);

-- Step 5: Create indexes
CREATE INDEX vehicles_vehicle_type_idx ON public.vehicles(vehicle_type);
CREATE INDEX vehicles_brand_idx ON public.vehicles(brand);
CREATE INDEX vehicles_model_idx ON public.vehicles(model);
CREATE INDEX vehicles_year_idx ON public.vehicles(year);
CREATE INDEX vehicles_motor_idx ON public.vehicles(motor);
CREATE INDEX vehicles_compound_idx ON public.vehicles(vehicle_type, brand, model, year, motor);

CREATE INDEX stages_vehicle_id_idx ON public.stages(vehicle_id);
CREATE INDEX stages_name_idx ON public.stages(name);
CREATE INDEX stages_stage_number_idx ON public.stages(stage_number);

-- Step 6: Create RPC functions
CREATE OR REPLACE FUNCTION get_distinct_vehicle_types()
RETURNS TABLE(vehicle_type text)
LANGUAGE sql
STABLE
AS $$
  SELECT DISTINCT v.vehicle_type
  FROM public.vehicles v
  WHERE v.vehicle_type IS NOT NULL
  ORDER BY v.vehicle_type;
$$;

CREATE OR REPLACE FUNCTION get_distinct_brands(p_vehicle_type text default null)
RETURNS TABLE(brand text)
LANGUAGE sql
STABLE
AS $$
  SELECT DISTINCT v.brand
  FROM public.vehicles v
  WHERE (p_vehicle_type IS NULL OR v.vehicle_type = p_vehicle_type)
    AND v.brand IS NOT NULL
  ORDER BY v.brand;
$$;

CREATE OR REPLACE FUNCTION get_distinct_models(
  p_vehicle_type text default null, 
  p_brand text default null
)
RETURNS TABLE(model text)
LANGUAGE sql
STABLE
AS $$
  SELECT DISTINCT v.model
  FROM public.vehicles v
  WHERE (p_vehicle_type IS NULL OR v.vehicle_type = p_vehicle_type)
    AND (p_brand IS NULL OR v.brand = p_brand)
    AND v.model IS NOT NULL
  ORDER BY v.model;
$$;

CREATE OR REPLACE FUNCTION get_distinct_years(
  p_vehicle_type text default null, 
  p_brand text default null,
  p_model text default null
)
RETURNS TABLE(year text)
LANGUAGE sql
STABLE
AS $$
  SELECT DISTINCT v.year
  FROM public.vehicles v
  WHERE (p_vehicle_type IS NULL OR v.vehicle_type = p_vehicle_type)
    AND (p_brand IS NULL OR v.brand = p_brand)
    AND (p_model IS NULL OR v.model = p_model)
    AND v.year IS NOT NULL
  ORDER BY v.year;
$$;

CREATE OR REPLACE FUNCTION get_distinct_motors(
  p_vehicle_type text default null, 
  p_brand text default null,
  p_model text default null,
  p_year text default null
)
RETURNS TABLE(motor text)
LANGUAGE sql
STABLE
AS $$
  SELECT DISTINCT v.motor
  FROM public.vehicles v
  WHERE (p_vehicle_type IS NULL OR v.vehicle_type = p_vehicle_type)
    AND (p_brand IS NULL OR v.brand = p_brand)
    AND (p_model IS NULL OR v.model = p_model)
    AND (p_year IS NULL OR v.year = p_year)
    AND v.motor IS NOT NULL
  ORDER BY v.motor;
$$;

-- Step 7: Create get_vehicles_with_stages function
CREATE OR REPLACE FUNCTION get_vehicles_with_stages(
  p_vehicle_type text default null,
  p_brand text default null,
  p_model text default null,
  p_year text default null,
  p_motor text default null,
  p_limit integer default null
)
RETURNS TABLE(
  id uuid,
  vehicle_type text,
  brand text,
  model text,
  year text,
  motor text,
  scraped_at timestamp with time zone,
  country text,
  total_stages integer,
  stages jsonb
)
LANGUAGE sql
STABLE
AS $$
  SELECT 
    v.id::uuid,
    v.vehicle_type::text,
    v.brand::text,
    v.model::text,
    v.year::text,
    v.motor::text,
    v.scraped_at::timestamp with time zone,
    v.country::text,
    v.total_stages::integer,
    COALESCE(
      jsonb_agg(
        jsonb_build_object(
          'name', s.name,
          'stage_number', s.stage_number,
          'raw_table_text', s.raw_table_text,
          'power_original', s.power_original,
          'power_tuned', s.power_tuned,
          'power_difference', s.power_difference,
          'torque_original', s.torque_original,
          'torque_tuned', s.torque_tuned,
          'torque_difference', s.torque_difference,
          'price', s.price,
          'price_uvp', s.price_uvp
        ) ORDER BY s.stage_number
      ) FILTER (WHERE s.id IS NOT NULL),
      '[]'::jsonb
    )::jsonb as stages
  FROM public.vehicles v
  LEFT JOIN public.stages s ON v.id = s.vehicle_id
  WHERE (p_vehicle_type IS NULL OR v.vehicle_type = p_vehicle_type)
    AND (p_brand IS NULL OR v.brand = p_brand)
    AND (p_model IS NULL OR v.model = p_model)
    AND (p_year IS NULL OR v.year = p_year)
    AND (p_motor IS NULL OR v.motor = p_motor)
  GROUP BY v.id, v.vehicle_type, v.brand, v.model, v.year, v.motor, v.scraped_at, v.country, v.total_stages
  ORDER BY v.brand, v.model, v.year, v.motor
  LIMIT p_limit;
$$;

-- Step 8: Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS trigger
LANGUAGE plpgsql
AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$;

-- Step 9: Apply updated_at triggers
CREATE TRIGGER update_vehicles_updated_at
  BEFORE UPDATE ON public.vehicles
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_stages_updated_at
  BEFORE UPDATE ON public.stages
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Step 10: Enable RLS
ALTER TABLE public.vehicles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.stages ENABLE ROW LEVEL SECURITY;

-- Step 11: Create policies (read-only for public)
CREATE POLICY "Public read access for vehicles" ON public.vehicles
  FOR SELECT USING (true);

CREATE POLICY "Public read access for stages" ON public.stages
  FOR SELECT USING (true);

-- Step 12: Grant permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT SELECT ON public.vehicles TO anon, authenticated;
GRANT SELECT ON public.stages TO anon, authenticated;
GRANT EXECUTE ON FUNCTION get_distinct_vehicle_types() TO anon, authenticated;
GRANT EXECUTE ON FUNCTION get_distinct_brands(text) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION get_distinct_models(text, text) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION get_distinct_years(text, text, text) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION get_distinct_motors(text, text, text, text) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION get_vehicles_with_stages(text, text, text, text, text, integer) TO anon, authenticated; 