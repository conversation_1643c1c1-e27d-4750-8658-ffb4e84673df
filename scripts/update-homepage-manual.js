// Manual script to update homepage with swipingServices
// Run this in Sanity Studio Vision or use sanity exec

const swipingServices = [
  {
    _type: 'swipingService',
    _key: 'tuning',
    text: 'Fahrzeugoptimierung',
    order: 1
  },
  {
    _type: 'swipingService', 
    _key: 'werkstatt',
    text: 'Werkstatt-Services',
    order: 2
  },
  {
    _type: 'swipingService',
    _key: 'aufbereitung', 
    text: 'Fahrzeugaufbereitung',
    order: 3
  },
  {
    _type: 'swipingService',
    _key: 'tuning-alt',
    text: 'Chiptuning', 
    order: 4
  }
]

// GROQ mutation to update homepage
const mutation = {
  patch: {
    id: 'homepage',
    set: {
      swipingServices: swipingServices
    }
  }
}

console.log('Copy this mutation and run it in Sanity Studio Vision:')
console.log(JSON.stringify(mutation, null, 2))

console.log('\nOr use this GROQ query to update:')
console.log(`
*[_type == "homepage"][0] {
  _id,
  _type,
  heroTitle,
  heroSubtitle,
  heroDescription,
  ctaButtonText,
  ctaSecondaryText,
  features,
  swipingServices
}
`)

export default function(client, context) {
  return client
    .patch('homepage')
    .set({swipingServices: swipingServices})
    .commit()
    .then(result => {
      console.log('Homepage updated successfully:', result)
      return result
    })
    .catch(err => {
      console.error('Error updating homepage:', err)
      throw err
    })
}
