#!/usr/bin/env python3
"""
Resume SMA extraction from existing batch files
"""

import json
import os

def rebuild_progress_from_batches():
    """Rebuild progress file from existing batch files"""
    
    # Find all batch files
    batch_dir = "../complete_extraction_selenium/"
    batch_files = []
    
    for filename in os.listdir(batch_dir):
        if filename.startswith("sma_batch_") and filename.endswith(".json"):
            batch_files.append(os.path.join(batch_dir, filename))
    
    if not batch_files:
        print("❌ No batch files found")
        return False
    
    print(f"📦 Found {len(batch_files)} batch files")
    
    # Load all vehicles from batches
    all_vehicles = []
    for batch_file in sorted(batch_files):
        print(f"📖 Loading {os.path.basename(batch_file)}")
        with open(batch_file, 'r', encoding='utf-8') as f:
            batch_data = json.load(f)
            all_vehicles.extend(batch_data)
    
    print(f"🚗 Total vehicles from batches: {len(all_vehicles)}")
    
    # Create combination IDs
    import hashlib
    completed_combinations = set()
    
    for vehicle in all_vehicles:
        combo = f"{vehicle['vehicle_type']}|{vehicle['brand']}|{vehicle['model']}|{vehicle['year']}|{vehicle['motor']}"
        combo_id = hashlib.md5(combo.encode()).hexdigest()
        completed_combinations.add(combo_id)
    
    # Create new progress file
    from datetime import datetime
    
    new_progress = {
        'completed_combinations': list(completed_combinations),
        'current_position': {},
        'total_estimated': 0,
        'last_save': datetime.now().isoformat(),
        'stats': {
            'vehicles_scraped': len(all_vehicles),
            'stages_extracted': sum(v.get('total_stages', 0) for v in all_vehicles),
            'errors': 0,
            'start_time': datetime.now().isoformat()
        }
    }
    
    # Save new progress
    progress_file = "../sma_extraction_progress.json"
    with open(progress_file, 'w', encoding='utf-8') as f:
        json.dump(new_progress, f, indent=2, ensure_ascii=False)
    
    print(f"✅ Updated progress file with {len(completed_combinations)} combinations")
    print(f"🎯 Ready to resume from vehicle {len(all_vehicles) + 1}")
    
    return True

if __name__ == "__main__":
    rebuild_progress_from_batches() 