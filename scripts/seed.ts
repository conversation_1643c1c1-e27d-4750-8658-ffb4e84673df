import { createClient } from '@supabase/supabase-js'

// Environment variables validation
const supabaseUrl = process.env.SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables:')
  console.error('SUPABASE_URL:', !!supabaseUrl)
  console.error('SUPABASE_SERVICE_KEY:', !!supabaseServiceKey)
  process.exit(1)
}

// Create admin client with service key for seeding
const supabase = createClient(supabaseUrl, supabaseServiceKey)

// Sample vehicle data
const sampleVehicles = [
  {
    id: 'bmw-320d-f30',
    vehicle_type: 'PKW',
    brand: 'BMW',
    model: '320d',
    year_range: '2012-2019',
    motor: 'N47D20C 184 PS',
    stages_count: 3,
  },
  {
    id: 'audi-a4-b9',
    vehicle_type: 'PKW',
    brand: 'Audi',
    model: 'A4',
    year_range: '2016-2023',
    motor: 'CRLB 2.0 TDI 190 PS',
    stages_count: 2,
  },
  {
    id: 'vw-golf-7-gti',
    vehicle_type: 'PKW',
    brand: 'Volkswagen',
    model: 'Golf VII GTI',
    year_range: '2013-2020',
    motor: 'EA888 2.0 TSI 230 PS',
    stages_count: 3,
  },
  {
    id: 'mercedes-c220d',
    vehicle_type: 'PKW',
    brand: 'Mercedes',
    model: 'C220d',
    year_range: '2014-2021',
    motor: 'OM651 2.1 CDI 170 PS',
    stages_count: 2,
  },
  {
    id: 'ford-transit-custom',
    vehicle_type: 'NFZ',
    brand: 'Ford',
    model: 'Transit Custom',
    year_range: '2018-2023',
    motor: 'DRFB 2.0 EcoBlue 130 PS',
    stages_count: 2,
  },
]

// Sample stage data
const sampleStages = [
  // BMW 320d stages
  {
    id: 'bmw-320d-stage1',
    vehicle_id: 'bmw-320d-f30',
    stage_name: 'Stage 1',
    original_power: '184 PS / 135 kW',
    tuned_power: '220 PS / 162 kW',
    power_gain_ps: '+36 PS',
    power_gain_kw: '+27 kW',
    price_chf: 890,
  },
  {
    id: 'bmw-320d-stage2',
    vehicle_id: 'bmw-320d-f30',
    stage_name: 'Stage 2',
    original_power: '184 PS / 135 kW',
    tuned_power: '245 PS / 180 kW',
    power_gain_ps: '+61 PS',
    power_gain_kw: '+45 kW',
    price_chf: 1290,
  },
  {
    id: 'bmw-320d-stage3',
    vehicle_id: 'bmw-320d-f30',
    stage_name: 'Stage 3',
    original_power: '184 PS / 135 kW',
    tuned_power: '270 PS / 199 kW',
    power_gain_ps: '+86 PS',
    power_gain_kw: '+64 kW',
    price_chf: 1890,
  },

  // Audi A4 stages
  {
    id: 'audi-a4-stage1',
    vehicle_id: 'audi-a4-b9',
    stage_name: 'Stage 1',
    original_power: '190 PS / 140 kW',
    tuned_power: '230 PS / 169 kW',
    power_gain_ps: '+40 PS',
    power_gain_kw: '+29 kW',
    price_chf: 950,
  },
  {
    id: 'audi-a4-stage2',
    vehicle_id: 'audi-a4-b9',
    stage_name: 'Stage 2',
    original_power: '190 PS / 140 kW',
    tuned_power: '255 PS / 188 kW',
    power_gain_ps: '+65 PS',
    power_gain_kw: '+48 kW',
    price_chf: 1390,
  },

  // VW Golf GTI stages
  {
    id: 'vw-golf-gti-stage1',
    vehicle_id: 'vw-golf-7-gti',
    stage_name: 'Stage 1',
    original_power: '230 PS / 169 kW',
    tuned_power: '280 PS / 206 kW',
    power_gain_ps: '+50 PS',
    power_gain_kw: '+37 kW',
    price_chf: 850,
  },
  {
    id: 'vw-golf-gti-stage2',
    vehicle_id: 'vw-golf-7-gti',
    stage_name: 'Stage 2',
    original_power: '230 PS / 169 kW',
    tuned_power: '320 PS / 235 kW',
    power_gain_ps: '+90 PS',
    power_gain_kw: '+66 kW',
    price_chf: 1250,
  },
  {
    id: 'vw-golf-gti-stage3',
    vehicle_id: 'vw-golf-7-gti',
    stage_name: 'Stage 3',
    original_power: '230 PS / 169 kW',
    tuned_power: '350 PS / 257 kW',
    power_gain_ps: '+120 PS',
    power_gain_kw: '+88 kW',
    price_chf: 1850,
  },

  // Mercedes C220d stages
  {
    id: 'mercedes-c220d-stage1',
    vehicle_id: 'mercedes-c220d',
    stage_name: 'Stage 1',
    original_power: '170 PS / 125 kW',
    tuned_power: '210 PS / 154 kW',
    power_gain_ps: '+40 PS',
    power_gain_kw: '+29 kW',
    price_chf: 920,
  },
  {
    id: 'mercedes-c220d-stage2',
    vehicle_id: 'mercedes-c220d',
    stage_name: 'Stage 2',
    original_power: '170 PS / 125 kW',
    tuned_power: '240 PS / 176 kW',
    power_gain_ps: '+70 PS',
    power_gain_kw: '+51 kW',
    price_chf: 1420,
  },

  // Ford Transit stages
  {
    id: 'ford-transit-stage1',
    vehicle_id: 'ford-transit-custom',
    stage_name: 'Stage 1',
    original_power: '130 PS / 96 kW',
    tuned_power: '160 PS / 118 kW',
    power_gain_ps: '+30 PS',
    power_gain_kw: '+22 kW',
    price_chf: 790,
  },
  {
    id: 'ford-transit-stage2',
    vehicle_id: 'ford-transit-custom',
    stage_name: 'Stage 2',
    original_power: '130 PS / 96 kW',
    tuned_power: '185 PS / 136 kW',
    power_gain_ps: '+55 PS',
    power_gain_kw: '+40 kW',
    price_chf: 1190,
  },
]

const seedDatabase = async (): Promise<void> => {
  try {
    console.log('🌱 Starting database seed...')

    // Clear existing data
    console.log('🧹 Clearing existing data...')
    await supabase.from('stages').delete().gte('id', '')
    await supabase.from('vehicles').delete().gte('id', '')

    // Insert vehicles
    console.log('🚗 Inserting vehicles...')
    const { error: vehiclesError } = await supabase
      .from('vehicles')
      .insert(sampleVehicles)

    if (vehiclesError) {
      throw new Error(`Failed to insert vehicles: ${vehiclesError.message}`)
    }

    // Insert stages
    console.log('⚡ Inserting tuning stages...')
    const { error: stagesError } = await supabase
      .from('stages')
      .insert(sampleStages)

    if (stagesError) {
      throw new Error(`Failed to insert stages: ${stagesError.message}`)
    }

    console.log('✅ Database seed completed successfully!')
    console.log(`📊 Inserted ${sampleVehicles.length} vehicles and ${sampleStages.length} stages`)
  } catch (error) {
    console.error('❌ Seed failed:', error)
    throw error
  }
}

// Run the seed if this file is executed directly
if (require.main === module) {
  seedDatabase()
    .then(() => {
      console.log('🎉 Seed process completed')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Seed process failed:', error)
      process.exit(1)
    })
}

export { seedDatabase } 