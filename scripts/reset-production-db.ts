#!/usr/bin/env tsx

import { createClient } from '@supabase/supabase-js'
import { readFileSync } from 'fs'
import { join } from 'path'

// Environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:')
  console.error('   NEXT_PUBLIC_SUPABASE_URL:', !!supabaseUrl)
  console.error('   SUPABASE_SERVICE_ROLE_KEY:', !!supabaseServiceKey)
  console.error('\nPlease set these in your .env.local file')
  process.exit(1)
}

console.log('🗄️ Resetting Production Database...')
console.log(`📍 Target: ${supabaseUrl}`)

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
})

async function resetDatabase() {
  try {
    console.log('\n🧹 STEP 1: Dropping all existing tables and functions...')
    
    // Drop everything in the right order
    const dropStatements = [
      'DROP TABLE IF EXISTS public.stages CASCADE;',
      'DROP TABLE IF EXISTS public.vehicles CASCADE;',
      'DROP FUNCTION IF EXISTS get_distinct_motors(text, text, text, text) CASCADE;',
      'DROP FUNCTION IF EXISTS get_distinct_models(text, text) CASCADE;',
      'DROP FUNCTION IF EXISTS get_distinct_brands(text) CASCADE;',
      'DROP FUNCTION IF EXISTS get_distinct_vehicle_types() CASCADE;',
      'DROP FUNCTION IF EXISTS get_distinct_years(text, text) CASCADE;',
      'DROP FUNCTION IF EXISTS get_distinct_engines(text, text, text) CASCADE;',
      'DROP FUNCTION IF EXISTS get_vehicles_with_stages(text, text, text, text, text, integer) CASCADE;',
      'DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;',
    ]
    
    for (const statement of dropStatements) {
      try {
        const { error } = await supabase.rpc('exec_sql', { sql: statement })
        if (error) {
          console.log(`   ⚠️  ${statement}: ${error.message}`)
        } else {
          console.log(`   ✅ ${statement}`)
        }
      } catch (err: any) {
        console.log(`   ⚠️  ${statement}: ${err.message}`)
      }
    }
    
    console.log('\n🏗️  STEP 2: Creating fresh schema...')
    
    // Read the base migration
    const migrationPath = join(process.cwd(), 'supabase', 'migrations', '20250622000001_create_vehicles_tables.sql')
    const migrationSQL = readFileSync(migrationPath, 'utf-8')
    
    // Execute statement by statement
    const statements = migrationSQL
      .split(';')
      .map(s => s.trim())
      .filter(s => s.length > 0 && !s.startsWith('--'))
      .map(s => s + ';')
    
    console.log(`   Found ${statements.length} SQL statements`)
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i]
      
      if (statement.trim() === ';') continue
      
      try {
        const { error } = await supabase.rpc('exec_sql', { sql: statement })
        
        if (error) {
          console.error(`   ❌ Statement ${i + 1} failed:`, error.message)
          console.log(`   SQL: ${statement.substring(0, 100)}...`)
        } else {
          console.log(`   ✅ Statement ${i + 1} executed`)
        }
      } catch (err: any) {
        console.log(`   ⚠️  Statement ${i + 1}: ${err.message}`)
      }
    }
    
    console.log('\n🔧 STEP 3: Applying schema fixes...')
    
    // Read the fix migration
    const fixMigrationPath = join(process.cwd(), 'supabase', 'migrations', '20250623000003_fix_schema_mismatch.sql')
    const fixMigrationSQL = readFileSync(fixMigrationPath, 'utf-8')
    
    // Execute the fix (but skip the rename since we're creating fresh)
    const fixStatements = fixMigrationSQL
      .split(';')
      .map(s => s.trim())
      .filter(s => s.length > 0 && !s.startsWith('--'))
      .filter(s => !s.includes('rename column engine to motor')) // Skip rename since we start fresh
      .map(s => s + ';')
    
    console.log(`   Found ${fixStatements.length} fix statements`)
    
    for (let i = 0; i < fixStatements.length; i++) {
      const statement = fixStatements[i]
      
      if (statement.trim() === ';') continue
      
      try {
        const { error } = await supabase.rpc('exec_sql', { sql: statement })
        
        if (error) {
          console.log(`   ⚠️  Fix ${i + 1}: ${error.message}`)
        } else {
          console.log(`   ✅ Fix ${i + 1} executed`)
        }
      } catch (err: any) {
        console.log(`   ⚠️  Fix ${i + 1}: ${err.message}`)
      }
    }
    
    console.log('\n🔍 STEP 4: Verifying new schema...')
    
    // Check tables
    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .in('table_name', ['vehicles', 'stages'])
    
    if (tablesError) {
      console.error('❌ Could not verify tables:', tablesError.message)
    } else {
      console.log('📊 Tables found:', tables?.map(t => t.table_name))
    }
    
    // Check vehicles table structure
    const { data: columns, error: columnsError } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type')
      .eq('table_schema', 'public')
      .eq('table_name', 'vehicles')
      .order('ordinal_position')
    
    if (columnsError) {
      console.error('❌ Could not verify columns:', columnsError.message)
    } else {
      console.log('📋 Vehicles table columns:')
      columns?.forEach(col => {
        console.log(`   - ${col.column_name}: ${col.data_type}`)
      })
    }
    
    console.log('\n🎉 Database reset completed successfully!')
    console.log('\n📝 Next steps:')
    console.log('   1. Run: pnpm tsx scripts/import-sma-data.ts')
    console.log('   2. Verify data import')
    console.log('   3. Test configurator API')
    
  } catch (error: any) {
    console.error('❌ Reset failed:', error.message)
    process.exit(1)
  }
}

// Execute if called directly
if (require.main === module) {
  resetDatabase()
} 