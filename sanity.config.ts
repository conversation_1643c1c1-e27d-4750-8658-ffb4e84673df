'use client'

/**
 * This configuration is used to for the Sanity Studio that's mounted on the `/app/studio/[[...tool]]/page.tsx` route
 */

import {visionTool} from '@sanity/vision'
import {defineConfig} from 'sanity'
import {structureTool} from 'sanity/structure'
import React from 'react'
import {schema} from './src/sanity/schemas'
import {structure} from './src/sanity/structure'
import {SupabaseVehicleManager} from './src/sanity/tools/SupabaseVehicleManager'

// Go to https://www.sanity.io/docs/api-versioning to learn how API versioning works
import {apiVersion, dataset, projectId} from './src/sanity/env'

// Custom Supabase Vehicle Management Tool
const supabaseVehicleTool = {
  name: 'supabase-vehicles',
  title: 'Fahrzeug-Datenbank',
  icon: () => '🗄️',
  component: SupabaseVehicleManager,
}



export default defineConfig({
  name: 'default',
  title: 'DB-Performance',
  basePath: '/studio',

  projectId,
  dataset,
  // Add and edit the content schema in the './sanity/schema' folder
  schema,
  plugins: [
    structureTool({
      structure,
    }),
    // Vision is for querying with GROQ from inside the Studio
    // https://www.sanity.io/docs/the-vision-plugin
    visionTool({defaultApiVersion: apiVersion}),
  ],
  tools: [supabaseVehicleTool],
})
