import type { Metadata } from 'next'
import { Manrope } from 'next/font/google'
import './globals.css'
import { strings } from '@/strings'
import { ConditionalLayout } from '@/components/ConditionalLayout'
import { FloatingWhatsApp } from '@/components/FloatingWhatsApp'

const manrope = Manrope({
  subsets: ['latin'],
  weight: ['400', '500', '700', '800'],
  display: 'swap',
})

export const metadata: Metadata = {
  title: {
    default: strings.home.title,
    template: `%s | ${strings.home.title}`,
  },
  description: strings.home.heroDescription,
  keywords: [
    'Auto-Tuning',
    'Chiptuning',
    'Fahrzeugoptimierung',
    'Schweiz',
    'Leuggern',
    'DB-Performance',
    'Motorleistung',
    'Tuning',
  ],
  authors: [{ name: 'DB-Performance Garage Bytyci' }],
  creator: 'DB-Performance Garage Bytyci',
  publisher: 'DB-Performance Garage Bytyci',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://db-performance.ch'),
  alternates: {
    canonical: '/',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="de-CH" className="scroll-smooth">
      <body className={manrope.className}>
        <ConditionalLayout>{children}</ConditionalLayout>
        <FloatingWhatsApp />
      </body>
    </html>
  )
} 