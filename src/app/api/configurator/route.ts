import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || process.env.SUPABASE_ANON_KEY;

// Validate environment variables
if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables:', {
    url: !!supabaseUrl,
    key: !!supabaseKey,
    env: process.env.NODE_ENV
  });
  throw new Error(`Missing Supabase environment variables. URL: ${!!supabaseUrl}, Key: ${!!supabaseKey}`);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Check if we're using production database (different function signatures)
const isProductionDB = supabaseUrl?.includes('nyokyvhgxvfzxaiahpxx');

interface TuningStage {
  stage: string;
  stage_number: number;
  raw_table_text: string;
  power_original: string;
  power_tuned: string;
  power_difference: string;
  price?: string;
  price_uvp?: string;
  torque_original: string;
  torque_tuned: string;
  torque_difference: string;
}

interface Vehicle {
  id: string;
  vehicle_type: string;
  brand: string;
  model: string;
  year: string;
  motor: string;
  scraped_at: string;
  country: string;
  total_stages: number;
  stages: TuningStage[];
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const vehicleType = searchParams.get('type');
    const brand = searchParams.get('brand');
    const model = searchParams.get('model');
    const year = searchParams.get('year');
    const motor = searchParams.get('motor');
    const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : null;
    const countOnly = searchParams.get('count') === 'true';
    const typesOnly = searchParams.get('types') === 'true';
    const brandsOnly = searchParams.get('brands') === 'true';

    console.log('🔍 API Request:', { vehicleType, brand, model, year, motor, limit, countOnly, typesOnly, brandsOnly, isProductionDB });

    // If types only is requested, return distinct vehicle types
    if (typesOnly) {
      const { data: typesData, error: typesError } = await supabase.rpc('get_distinct_vehicle_types');
      
      if (typesError) {
        console.error('❌ Error fetching vehicle types:', typesError);
        return NextResponse.json({ error: 'Failed to fetch vehicle types' }, { status: 500 });
      }

      // Sort with PKW first, then alphabetically
      const sortedTypes = (typesData || []).sort((a: { vehicle_type: string }, b: { vehicle_type: string }) => {
        const aType = a.vehicle_type;
        const bType = b.vehicle_type;
        
        // PKW always comes first
        if (aType === 'Kraftfahrzeuge (PKW)') return -1;
        if (bType === 'Kraftfahrzeuge (PKW)') return 1;
        
        // Then alphabetical
        return aType.localeCompare(bType);
      });

      return NextResponse.json(sortedTypes);
    }

    // If brands only is requested, return distinct brands for the vehicle type
    if (brandsOnly && vehicleType) {
      const { data: brandsData, error: brandsError } = await supabase.rpc('get_distinct_brands', { 
        p_vehicle_type: vehicleType 
      });
      
      if (brandsError) {
        console.error('❌ Error fetching brands:', brandsError);
        return NextResponse.json({ error: 'Failed to fetch brands' }, { status: 500 });
      }

      return NextResponse.json(brandsData || []);
    }

    // If count only is requested, return just the count
    if (countOnly) {
      let query = supabase
        .from('vehicles')
        .select('*', { count: 'exact', head: true });

      if (vehicleType) {
        query = query.eq('vehicle_type', vehicleType);
      }

      const { count, error } = await query;

      if (error) {
        console.error('Database error:', error);
        return NextResponse.json({ error: 'Failed to count vehicles' }, { status: 500 });
      }

      return NextResponse.json({ count: count || 0 });
    }

    // Use the database function to get vehicles with stages
    const { data, error } = await supabase.rpc('get_vehicles_with_stages', {
      p_vehicle_type: vehicleType || null,
      p_brand: brand || null,
      p_model: model || null,
      p_year: year || null,
      p_motor: motor || null,
      p_limit: limit
    });

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json({ error: 'Failed to load vehicle data' }, { status: 500 });
    }

    // Database response interfaces
    interface VehicleRowResponse {
      id: string;
      vehicle_type: string;
      brand: string;
      model: string;
      year: string;
      motor: string;
      scraped_at: string;
      country: string;
      total_stages: number;
      stages: TuningStage[];
    }

    // Transform the data to match the expected format
    const vehicles: Vehicle[] = data.map((row: VehicleRowResponse) => ({
      id: row.id,
      vehicle_type: row.vehicle_type,
      brand: row.brand,
      model: row.model,
      year: row.year,
      motor: row.motor,
      scraped_at: row.scraped_at,
      country: row.country,
      total_stages: row.total_stages,
      stages: row.stages || []
    }));

    console.log(`✅ Loaded ${vehicles.length} vehicles from database`);

    return NextResponse.json(vehicles);
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// GET distinct values for filters
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, vehicleType, brand, model, year } = body;

    let data, error;

    switch (action) {
      case 'vehicle_types':
        ({ data, error } = await supabase.rpc('get_distinct_vehicle_types'));
        break;
      
      case 'brands':
        ({ data, error } = await supabase.rpc('get_distinct_brands', {
          p_vehicle_type: vehicleType || null
        }));
        break;
      
      case 'models':
        if (isProductionDB) {
          // Production DB doesn't have get_distinct_models function, use direct query
          let query = supabase
            .from('vehicles')
            .select('model')
            .order('model');
          
          if (vehicleType) query = query.eq('vehicle_type', vehicleType);
          if (brand) query = query.eq('brand', brand);
          
          const result = await query;
          if (result.data) {
            // Remove duplicates and map to expected format
            const uniqueModels = [...new Set(result.data.map(row => row.model))];
            data = uniqueModels.map(model => ({ model }));
          }
          error = result.error;
        } else {
          // Local DB has the function
          ({ data, error } = await supabase.rpc('get_distinct_models', {
            p_vehicle_type: vehicleType || null,
            p_brand: brand || null
          }));
        }
        break;
      
      case 'years':
        if (isProductionDB) {
          // Production DB function only takes vehicle_type and brand
          ({ data, error } = await supabase.rpc('get_distinct_years', {
            p_vehicle_type: vehicleType || null,
            p_brand: brand || null
          }));
          
          // Filter by model client-side if model is provided
          if (data && model) {
            // Get all vehicles with this model to filter years
            const modelResult = await supabase
              .from('vehicles')
              .select('year')
              .eq('vehicle_type', vehicleType)
              .eq('brand', brand)
              .eq('model', model);
            
            if (modelResult.data) {
              const modelYears = [...new Set(modelResult.data.map(row => row.year))];
              data = data.filter((yearRow: { year: string }) => modelYears.includes(yearRow.year));
            }
          }
        } else {
          // Local DB function takes model parameter too
          ({ data, error } = await supabase.rpc('get_distinct_years', {
            p_vehicle_type: vehicleType || null,
            p_brand: brand || null,
            p_model: model || null
          }));
        }
        break;
      
      case 'motors':
        if (isProductionDB) {
          // Production DB doesn't have get_distinct_engines/motors function, use direct query
          let query = supabase
            .from('vehicles')
            .select('motor')
            .order('motor');
          
          if (vehicleType) query = query.eq('vehicle_type', vehicleType);
          if (brand) query = query.eq('brand', brand);
          if (model) query = query.eq('model', model);
          if (year) query = query.eq('year', year);
          
          const result = await query;
          if (result.data) {
            // Remove duplicates and map to expected format
            const uniqueMotors = [...new Set(result.data.map(row => row.motor))];
            data = uniqueMotors.map(motor => ({ motor }));
          }
          error = result.error;
        } else {
          // Local DB has get_distinct_motors function
          ({ data, error } = await supabase.rpc('get_distinct_motors', {
            p_vehicle_type: vehicleType || null,
            p_brand: brand || null,
            p_model: model || null,
            p_year: year || null
          }));
        }
        break;
      
      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json({ error: 'Failed to load data' }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 