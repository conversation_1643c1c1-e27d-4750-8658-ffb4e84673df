import { NextResponse } from 'next/server'

export async function GET() {
  const html = `
<!DOCTYPE html>
<html lang="de">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Fahrzeug-Verwaltung</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body class="bg-gray-50 min-h-screen">
  <div id="app">
    <div class="flex items-center justify-center h-screen">
      <div class="text-xl">Fahrzeuge werden geladen...</div>
    </div>
  </div>

  <script>
    // Supabase client setup
    const { createClient } = supabase;
    const supabaseClient = createClient(
      '${process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL}',
      '${process.env.SUPABASE_ANON_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}'
    );

    let vehicles = [];
    let filteredVehicles = [];
    let searchTerm = '';
    let selectedVehicle = null;
    let isEditing = false;

    // Fetch vehicles from Supabase
    async function fetchVehicles() {
      try {
        const { data, error } = await supabaseClient
          .from('vehicles')
          .select(\`
            *,
            stages (*)
          \`)
          .order('brand', { ascending: true })
          .order('model', { ascending: true })
          .limit(100);

        if (error) throw error;
        vehicles = data || [];
        applySearch();
        renderApp();
      } catch (error) {
        console.error('Error fetching vehicles:', error);
        renderError();
      }
    }

    // Apply search filter
    function applySearch() {
      filteredVehicles = vehicles.filter(vehicle =>
        vehicle.brand.toLowerCase().includes(searchTerm.toLowerCase()) ||
        vehicle.model.toLowerCase().includes(searchTerm.toLowerCase()) ||
        vehicle.engine.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Save vehicle changes
    async function saveVehicle() {
      if (!selectedVehicle) return;

      try {
        // Update vehicle
        const { error: vehicleError } = await supabaseClient
          .from('vehicles')
          .update({
            vehicle_type: selectedVehicle.vehicle_type,
            brand: selectedVehicle.brand,
            model: selectedVehicle.model,
            year: selectedVehicle.year,
            engine: selectedVehicle.engine,
          })
          .eq('id', selectedVehicle.id);

        if (vehicleError) throw vehicleError;

        // Delete existing stages
        await supabaseClient.from('stages').delete().eq('vehicle_id', selectedVehicle.id);

        // Insert new stages
        if (selectedVehicle.stages && selectedVehicle.stages.length > 0) {
          const stageInserts = selectedVehicle.stages.map(stage => ({
            vehicle_id: selectedVehicle.id,
            name: stage.name,
            original_power: stage.original_power,
            tuned_power: stage.tuned_power,
            price_chf: stage.price_chf,
            description: stage.description,
          }));

          const { error: stagesError } = await supabaseClient
            .from('stages')
            .insert(stageInserts);

          if (stagesError) throw stagesError;
        }

        await fetchVehicles();
        isEditing = false;
        selectedVehicle = null;
        alert('Fahrzeug erfolgreich aktualisiert!');
      } catch (error) {
        console.error('Error updating vehicle:', error);
        alert('Fehler beim Speichern!');
      }
    }

    // Render the application
    function renderApp() {
      const app = document.getElementById('app');
      
      if (isEditing && selectedVehicle) {
        app.innerHTML = renderEditForm();
        bindEditFormEvents();
      } else {
        app.innerHTML = renderVehicleList();
        bindListEvents();
      }
    }

    // Render vehicle list
    function renderVehicleList() {
      return \`
        <div class="max-w-7xl mx-auto p-6">
          <h1 class="text-3xl font-bold text-gray-900 mb-8">Fahrzeug-Verwaltung</h1>
          
          <!-- Search -->
          <div class="mb-6">
            <input
              type="text"
              id="searchInput"
              placeholder="Suche nach Marke, Modell oder Motor..."
              value="\${searchTerm}"
              class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <!-- Results count -->
          <div class="text-sm text-gray-600 mb-4">
            \${filteredVehicles.length} Fahrzeuge gefunden
          </div>

          <!-- Vehicle List -->
          <div class="grid gap-4">
            \${filteredVehicles.map(vehicle => \`
              <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex justify-between items-start">
                  <div class="flex-1">
                    <h3 class="text-lg font-semibold text-gray-900">
                      \${vehicle.brand} \${vehicle.model} \${vehicle.year ? \`(\${vehicle.year})\` : ''}
                    </h3>
                    <p class="text-gray-600 mb-2">\${vehicle.engine}</p>
                    <div class="text-sm text-gray-500">
                      Typ: \${vehicle.vehicle_type} • \${vehicle.stages?.length || 0} Tuning-Stages
                    </div>
                    \${vehicle.stages && vehicle.stages.length > 0 ? \`
                      <div class="mt-3">
                        <h4 class="font-medium text-gray-700 mb-2">Verfügbare Stages:</h4>
                        <div class="space-y-1">
                          \${vehicle.stages.map(stage => \`
                            <div class="text-sm text-gray-600">
                              \${stage.name}: \${stage.tuned_power || 'N/A'}HP - CHF \${stage.price_chf}
                            </div>
                          \`).join('')}
                        </div>
                      </div>
                    \` : ''}
                  </div>
                  <button
                    onclick="editVehicle(\${vehicle.id})"
                    class="ml-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Bearbeiten
                  </button>
                </div>
              </div>
            \`).join('')}
          </div>
        </div>
      \`;
    }

    // Render edit form
    function renderEditForm() {
      return \`
        <div class="max-w-7xl mx-auto p-6">
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 class="text-xl font-semibold mb-6">Fahrzeug bearbeiten</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Marke</label>
                <input type="text" id="brand" value="\${selectedVehicle.brand}" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" />
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Modell</label>
                <input type="text" id="model" value="\${selectedVehicle.model}" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" />
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Baujahr</label>
                <input type="text" id="year" value="\${selectedVehicle.year || ''}" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" />
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Motor</label>
                <input type="text" id="engine" value="\${selectedVehicle.engine}" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" />
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Fahrzeugtyp</label>
                <select id="vehicleType" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                  <option value="car" \${selectedVehicle.vehicle_type === 'car' ? 'selected' : ''}>Auto</option>
                  <option value="motorcycle" \${selectedVehicle.vehicle_type === 'motorcycle' ? 'selected' : ''}>Motorrad</option>
                  <option value="truck" \${selectedVehicle.vehicle_type === 'truck' ? 'selected' : ''}>LKW</option>
                </select>
              </div>
            </div>

            <!-- Stages -->
            <div class="mb-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">Tuning-Stages</h3>
              <div id="stagesContainer">
                \${renderStages()}
              </div>
              <button onclick="addStage()" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                Neue Stage hinzufügen
              </button>
            </div>

            <!-- Action Buttons -->
            <div class="flex space-x-4">
              <button onclick="saveVehicle()" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                Speichern
              </button>
              <button onclick="cancelEdit()" class="px-6 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors">
                Abbrechen
              </button>
            </div>
          </div>
        </div>
      \`;
    }

    // Render stages
    function renderStages() {
      return selectedVehicle.stages.map((stage, index) => \`
        <div class="border border-gray-200 rounded-lg p-4 mb-4">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Stage Name</label>
              <input type="text" id="stageName_\${index}" value="\${stage.name}" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Originalleistung (HP)</label>
              <input type="number" id="originalPower_\${index}" value="\${stage.original_power || ''}" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Geleistete Leistung (HP)</label>
              <input type="number" id="tunedPower_\${index}" value="\${stage.tuned_power || ''}" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Preis (CHF)</label>
              <input type="number" id="price_\${index}" value="\${stage.price_chf}" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" />
            </div>
            
            <div class="md:col-span-2">
              <label class="block text-sm font-medium text-gray-700 mb-1">Beschreibung</label>
              <textarea id="description_\${index}" rows="2" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">\${stage.description || ''}</textarea>
            </div>
          </div>
          
          <button onclick="removeStage(\${index})" class="mt-2 text-red-600 hover:text-red-800 text-sm">
            Stage löschen
          </button>
        </div>
      \`).join('');
    }

    // Event binding functions
    function bindListEvents() {
      const searchInput = document.getElementById('searchInput');
      if (searchInput) {
        searchInput.addEventListener('input', (e) => {
          searchTerm = e.target.value;
          applySearch();
          renderApp();
        });
      }
    }

    function bindEditFormEvents() {
      // Basic vehicle fields
      ['brand', 'model', 'year', 'engine', 'vehicleType'].forEach(field => {
        const element = document.getElementById(field);
        if (element) {
          element.addEventListener('input', (e) => {
            const key = field === 'vehicleType' ? 'vehicle_type' : field;
            selectedVehicle[key] = e.target.value;
          });
        }
      });

      // Stage fields
      selectedVehicle.stages.forEach((stage, index) => {
        ['stageName', 'originalPower', 'tunedPower', 'price', 'description'].forEach(field => {
          const element = document.getElementById(\`\${field}_\${index}\`);
          if (element) {
            element.addEventListener('input', (e) => {
              const key = {
                stageName: 'name',
                originalPower: 'original_power',
                tunedPower: 'tuned_power',
                price: 'price_chf',
                description: 'description'
              }[field];
              
              let value = e.target.value;
              if (['original_power', 'tuned_power', 'price_chf'].includes(key)) {
                value = value ? parseInt(value) : undefined;
              }
              
              selectedVehicle.stages[index][key] = value;
            });
          }
        });
      });
    }

    // Global functions
    window.editVehicle = function(id) {
      selectedVehicle = JSON.parse(JSON.stringify(vehicles.find(v => v.id === id)));
      isEditing = true;
      renderApp();
    };

    window.saveVehicle = saveVehicle;

    window.cancelEdit = function() {
      isEditing = false;
      selectedVehicle = null;
      renderApp();
    };

    window.addStage = function() {
      selectedVehicle.stages.push({
        name: 'Stage 1',
        original_power: undefined,
        tuned_power: undefined,
        price_chf: 0,
        description: '',
      });
      renderApp();
    };

    window.removeStage = function(index) {
      selectedVehicle.stages.splice(index, 1);
      renderApp();
    };

    function renderError() {
      document.getElementById('app').innerHTML = \`
        <div class="flex items-center justify-center h-screen">
          <div class="text-xl text-red-500">Fehler beim Laden der Fahrzeuge</div>
        </div>
      \`;
    }

    // Initialize app
    fetchVehicles();
  </script>
</body>
</html>
  `;

  return new NextResponse(html, {
    headers: {
      'Content-Type': 'text/html',
    },
  });
} 