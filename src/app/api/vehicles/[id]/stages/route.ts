import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/db/supa'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: vehicleId } = await params

    if (!vehicleId) {
      return NextResponse.json(
        { error: 'Vehicle ID is required' },
        { status: 400 }
      )
    }

    // Get stages for the specific vehicle
    const stages = await db.stages.getByVehicleId(vehicleId)

    return NextResponse.json({
      data: stages,
      count: stages.length,
    })
  } catch (error) {
    console.error('Error in stages API:', error)
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 