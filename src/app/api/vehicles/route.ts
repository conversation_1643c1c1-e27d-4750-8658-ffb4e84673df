import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { db } from '@/db/supa'

// Validation schema for query parameters
const searchParamsSchema = z.object({
  type: z.string().optional(),
  brand: z.string().optional(),
  year: z.string().optional(),
  engine: z.string().optional(),
  search: z.string().optional(),
  _options: z.string().optional(), // Special parameter to get filter options
})

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Parse and validate query parameters
    const params = searchParamsSchema.parse({
      type: searchParams.get('type') || undefined,
      brand: searchParams.get('brand') || undefined,
      year: searchParams.get('year') || undefined,
      engine: searchParams.get('engine') || undefined,
      search: searchParams.get('search') || undefined,
      _options: searchParams.get('_options') || undefined,
    })

    // If options are requested, return filter options
    if (params._options) {
      const options = await db.vehicles.getFilterOptions({
        type: params.type,
        brand: params.brand,
      })
      return NextResponse.json(options)
    }

    // Get vehicles with applied filters
    const vehicles = await db.vehicles.getAll(params)

    return NextResponse.json(vehicles)
  } catch (error) {
    console.error('Error in vehicles API:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid query parameters', details: error.issues },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

 