import Link from 'next/link';
import { strings } from '../../strings';
import { sanityService } from '../../sanity/services/SanityService';
import { ServiceWhatsAppButton } from '@/components/ServiceWhatsAppButton';

export const metadata = {
  title: `${strings.nav.services} | ${strings.home.title}`,
  description: 'Professionelle Chiptuning-Dienstleistungen bei DB-Performance: Kennfeldoptimierung, Leistungssteigerung, Motortuning und mehr.',
};

// Service card interface
interface ServiceCard {
  title: string;
  description: string;
  features: string[];
  buttonText: string;
  buttonLink: string;
  isExternalLink: boolean;
  color: string;
  icon: string;
  order: number;
}

// Services page data interface
interface ServicesPageData {
  heroTitle: string;
  heroSubtitle: string;
  heroDescription: string;
  services: ServiceCard[];
  ctaTitle: string;
  ctaDescription: string;
  ctaButtonText: string;
}

// Icon component
const ServiceIcon = ({ icon, className }: { icon: string; className: string }) => {
  const icons = {
    lightning: (
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
    ),
    chart: (
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
    ),
    cog: (
      <>
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
      </>
    ),
    clock: (
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
    ),
    beaker: (
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
    ),
    chat: (
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z" />
    ),
  };

  return (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
      {icons[icon as keyof typeof icons] || icons.lightning}
    </svg>
  );
};



// Default fallback data
const defaultServicesData: ServicesPageData = {
  heroTitle: 'Unsere Dienstleistungen',
  heroSubtitle: 'Professionelle Fahrzeugoptimierung auf höchstem Niveau',
  heroDescription: 'Von der Kennfeldoptimierung bis zur kompletten Leistungssteigerung - wir bieten individuelle Lösungen für Ihr Fahrzeug.',
  services: [
    {
      title: 'Chiptuning',
      description: 'Professionelle Kennfeldoptimierung für mehr Leistung, Drehmoment und besseren Kraftstoffverbrauch.',
      features: ['Bis zu 30% mehr Leistung', 'Optimierter Kraftstoffverbrauch', 'Erhaltung der Garantie'],
      buttonText: 'Konfigurator starten',
      buttonLink: '/tuning',
      isExternalLink: false,
      color: 'blue',
      icon: 'lightning',
      order: 1,
    },
    {
      title: 'Leistungsmessung',
      description: 'Präzise Messung auf unserem Allrad-Leistungsprüfstand für exakte Ergebnisse vor und nach dem Tuning.',
      features: ['Allrad-Prüfstand', 'Vorher/Nachher Messung', 'Detaillierter Bericht'],
      buttonText: 'Termin vereinbaren',
      buttonLink: '#contact',
      isExternalLink: false,
      color: 'green',
      icon: 'chart',
      order: 2,
    },
    {
      title: 'Scheiben ersetzen über Versicherung',
      description: 'Wir machen alles für dich - komplette Abwicklung über die Versicherung mit Ersatzwagen falls gewünscht.',
      features: ['Komplette Versicherungsabwicklung', 'Ersatzwagen verfügbar', 'Professionelle Montage'],
      buttonText: 'Beratung anfragen',
      buttonLink: '#contact',
      isExternalLink: false,
      color: 'orange',
      icon: 'cog',
      order: 3,
    },
    {
      title: 'Motorrad Tuning',
      description: 'Spezielle Optimierungen für Motorräder, Quads und andere Zweiräder für maximale Performance.',
      features: ['Motorrad-Spezialisten', 'Quick-Shifter Anpassung', 'Drehzahlbegrenzer-off'],
      buttonText: 'Modelle anzeigen',
      buttonLink: '/tuning',
      isExternalLink: false,
      color: 'purple',
      icon: 'clock',
      order: 4,
    },
    {
      title: 'Sonderfahrzeuge',
      description: 'Tuning für Boote, Jet-Skis, Traktoren und andere Spezialfahrzeuge mit individuellen Lösungen.',
      features: ['Marine-Motoren', 'Landmaschinen', 'Spezialanfertigungen'],
      buttonText: 'Verfügbarkeit prüfen',
      buttonLink: '/tuning',
      isExternalLink: false,
      color: 'red',
      icon: 'beaker',
      order: 5,
    },
    {
      title: 'Beratung & Service',
      description: 'Umfassende Beratung, After-Sales-Service und Support für alle unsere Tuning-Lösungen.',
      features: ['Kostenlose Erstberatung', '24/7 Support', 'Lebenslange Garantie'],
      buttonText: 'Kontakt aufnehmen',
      buttonLink: '#contact',
      isExternalLink: false,
      color: 'teal',
      icon: 'chat',
      order: 6,
    },
  ],
  ctaTitle: 'Bereit für mehr Leistung?',
  ctaDescription: 'Entdecken Sie das Potenzial Ihres Fahrzeugs mit unserem Tuning-Konfigurator',
  ctaButtonText: 'Konfigurator starten',
};

// Helper function to get consistent Swiss Auto Care colors (like homepage)
function getSwissAutoColors() {
  // All services use the same accent red color for consistency
  return {
    border: 'border-accent-red',
    iconBg: 'bg-accent-red',
    checkBg: 'bg-accent-red',
    buttonBg: 'bg-accent-red',
    buttonHover: 'hover:bg-red-600'
  };
}





export default async function ServicesPage() {
  let servicesData: ServicesPageData;
  
  try {
    // Try to fetch from Sanity, fallback to default data
    servicesData = await sanityService.getServicesPage() as ServicesPageData;
    if (!servicesData) {
      servicesData = defaultServicesData;
    }
  } catch (error) {
    console.log('Using default services data (Sanity not available):', error);
    servicesData = defaultServicesData;
  }
  return (
    <div className="min-h-screen">

      {/* Hero Section */}
      <section className="relative flex flex-col items-center justify-center min-h-[40vh] sm:min-h-[50vh] bg-cover bg-center bg-no-repeat text-white" style={{
        backgroundImage: 'linear-gradient(rgba(58, 58, 58, 0.8) 0%, rgba(58, 58, 58, 0.9) 100%), url("/DBP_M3_Lucas.jpeg")',
        filter: 'grayscale(100%)'
      }}>
        <div className="text-center p-4 sm:p-8">
          <h1 className="text-3xl font-extrabold leading-tight tracking-tighter sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl">
            {servicesData.heroTitle}
          </h1>
          <p className="mt-4 max-w-2xl mx-auto text-base sm:text-lg text-gray-300 px-4">
            {servicesData.heroSubtitle}
          </p>
          <p className="mt-2 max-w-2xl mx-auto text-sm sm:text-base text-gray-400 px-4">
            {servicesData.heroDescription}
          </p>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-8 sm:py-12" id="services">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-extrabold text-anthracite sm:text-4xl">Unsere Dienstleistungen</h2>
            <p className="mt-4 text-lg text-gray-600">Wir bieten eine breite Palette an Services, um Ihr Fahrzeug zu perfektionieren.</p>
          </div>
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">

            {servicesData.services.map((service, index) => {
              const colors = getSwissAutoColors();
              return (
                <div key={index} className="bg-gray-50 rounded-lg shadow-md p-6 flex flex-col items-center text-center transition-transform hover:scale-105 hover:shadow-xl relative">
                  {/* Dezenter WhatsApp Button unten rechts */}
                  <ServiceWhatsAppButton serviceTitle={service.title} />

                  <div className={`${colors.iconBg} text-white rounded-full p-4 mb-4`}>
                    <ServiceIcon icon={service.icon} className="w-8 h-8" />
                  </div>
                  <h3 className="text-xl font-bold text-anthracite mb-2">{service.title}</h3>
                  <p className="text-gray-600 mb-4 flex-grow">
                    {service.description}
                  </p>
                  <ul className="space-y-2 mb-6 text-left">
                    {service.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-sm text-gray-700">
                        <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                        {feature}
                      </li>
                    ))}
                  </ul>
                  {service.isExternalLink ? (
                    <a
                      href={service.buttonLink}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="font-semibold text-accent-red hover:underline"
                    >
                      {service.buttonText}
                    </a>
                  ) : (
                    service.buttonLink.startsWith('#') ? (
                      <a
                        href={service.buttonLink}
                        className="font-semibold text-accent-red hover:underline"
                      >
                        {service.buttonText}
                      </a>
                    ) : (
                      <Link
                        href={service.buttonLink as '/tuning'}
                        className="font-semibold text-accent-red hover:underline"
                      >
                        {service.buttonText}
                      </Link>
                    )
                  )}
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-anthracite text-white py-16 sm:py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-extrabold sm:text-4xl">
            {servicesData.ctaTitle}
          </h2>
          <p className="mt-4 text-lg text-gray-300">
            {servicesData.ctaDescription}
          </p>
          <div className="mt-8">
            <Link
              href="/tuning"
              className="flex min-w-[84px] cursor-pointer items-center justify-center overflow-hidden rounded-md h-12 px-6 bg-accent-red text-white text-base font-bold shadow-xl transition-transform hover:scale-105 mx-auto"
            >
              <span className="truncate">{servicesData.ctaButtonText}</span>
            </Link>
          </div>
        </div>
      </section>

    </div>
  );
} 