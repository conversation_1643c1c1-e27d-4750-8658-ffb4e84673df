import { strings } from '../../strings';
import { sanityService } from '@/sanity/services/SanityService';

import { ServiceWhatsAppButton } from '@/components/ServiceWhatsAppButton';

export const metadata = {
  title: `${strings.detailing.title} | ${strings.home.title}`,
  description: strings.detailing.subtitle,
};

// Service card interface
interface DetailingServiceCard {
  title: string;
  description: string;
  features: readonly string[];
  buttonText: string;
  buttonLink: string;
  isExternalLink: boolean;
  color: string;
  icon: string;
  order: number;
}

// Detailing page data interface
interface DetailingPageData {
  heroTitle: string;
  heroSubtitle: string;
  heroDescription: string;
  services: DetailingServiceCard[];
  ctaTitle: string;
  ctaDescription: string;
  ctaButtonText: string;
}

// Icon component for detailing services
const DetailingIcon = ({ icon, className }: { icon: string; className: string }) => {
  const icons = {
    polish: (
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
    ),
    shield: (
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
    ),
    sparkles: (
      <>
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
      </>
    ),
    car: (
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17a2 2 0 11-4 0 2 2 0 014 0zM19 17a2 2 0 11-4 0 2 2 0 014 0z M13 16H8.5a3.5 3.5 0 00-3.5 3.5v.5M17 16v.5a3.5 3.5 0 003.5 3.5h.5M3 12h18l-2-7H5l-2 7zM5 12v7h14v-7" />
    ),
  };

  return (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
      {icons[icon as keyof typeof icons] || icons.sparkles}
    </svg>
  );
};



// Default fallback data
const defaultDetailingData: DetailingPageData = {
  heroTitle: strings.detailing.title,
  heroSubtitle: strings.detailing.subtitle,
  heroDescription: strings.detailing.heroDescription,
  services: [
    {
      title: strings.detailing.polishing.title,
      description: strings.detailing.polishing.description,
      features: strings.detailing.polishing.features,
      buttonText: 'Termin vereinbaren',
      buttonLink: 'mailto:<EMAIL>',
      isExternalLink: true,
      color: 'blue',
      icon: 'polish',
      order: 1,
    },
    {
      title: strings.detailing.ceramic.title,
      description: strings.detailing.ceramic.description,
      features: strings.detailing.ceramic.features,
      buttonText: 'Beratung anfragen',
      buttonLink: 'mailto:<EMAIL>',
      isExternalLink: true,
      color: 'purple',
      icon: 'shield',
      order: 2,
    },
    {
      title: strings.detailing.wax.title,
      description: strings.detailing.wax.description,
      features: strings.detailing.wax.features,
      buttonText: 'Angebot anfordern',
      buttonLink: 'mailto:<EMAIL>',
      isExternalLink: true,
      color: 'orange',
      icon: 'sparkles',
      order: 3,
    },
    {
      title: strings.detailing.interior.title,
      description: strings.detailing.interior.description,
      features: strings.detailing.interior.features,
      buttonText: 'Termin buchen',
      buttonLink: 'mailto:<EMAIL>',
      isExternalLink: true,
      color: 'teal',
      icon: 'car',
      order: 4,
    },
  ],
  ctaTitle: strings.detailing.ctaTitle,
  ctaDescription: strings.detailing.ctaDescription,
  ctaButtonText: strings.detailing.ctaButtonText,
};

// Convert Sanity data to component format
const convertSanityDetailingData = (sanityData: unknown): DetailingPageData => {
  if (!sanityData) return defaultDetailingData;

  const data = sanityData as {
    heroSection?: {
      heroTitle?: string;
      heroSubtitle?: string;
      heroDescription?: string;
    };
    services?: Array<{
      title?: string;
      description?: string;
      features?: string[];
      buttonText?: string;
      buttonLink?: string;
      isExternalLink?: boolean;
      color?: string;
      icon?: string;
      order?: number;
    }>;
    ctaSection?: {
      ctaTitle?: string;
      ctaDescription?: string;
      ctaButtonText?: string;
    };
  };

  return {
    heroTitle: data.heroSection?.heroTitle || strings.detailing.title,
    heroSubtitle: data.heroSection?.heroSubtitle || strings.detailing.subtitle,
    heroDescription: data.heroSection?.heroDescription || strings.detailing.heroDescription,
    services: data.services?.map((service) => ({
      title: service.title || '',
      description: service.description || '',
      features: service.features || [],
      buttonText: service.buttonText || 'Termin vereinbaren',
      buttonLink: service.buttonLink || 'mailto:<EMAIL>',
      isExternalLink: service.isExternalLink ?? true,
      color: service.color || 'blue',
      icon: service.icon || 'sparkles',
      order: service.order || 1,
    })).sort((a, b) => a.order - b.order) || [],
    ctaTitle: data.ctaSection?.ctaTitle || strings.detailing.ctaTitle,
    ctaDescription: data.ctaSection?.ctaDescription || strings.detailing.ctaDescription,
    ctaButtonText: data.ctaSection?.ctaButtonText || strings.detailing.ctaButtonText,
  };
};

export default async function DetailingPage() {
  let detailingData: DetailingPageData;
  
  try {
    // Try to fetch from Sanity
    const sanityDetailingData = await sanityService.getDetailingPage();
    detailingData = sanityDetailingData ? convertSanityDetailingData(sanityDetailingData) : defaultDetailingData;
  } catch (error) {
    console.log('Using default detailing data (Sanity not available):', error);
    detailingData = defaultDetailingData;
  }

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative flex flex-col items-center justify-center min-h-[50vh] bg-cover bg-center bg-no-repeat text-white" style={{
        backgroundImage: 'linear-gradient(rgba(58, 58, 58, 0.8) 0%, rgba(58, 58, 58, 0.9) 100%), url("/DBP_M3_Lucas.jpeg")',
        filter: 'grayscale(100%)'
      }}>
        <div className="text-center p-8">
          <h1 className="text-5xl font-extrabold leading-tight tracking-tighter sm:text-6xl md:text-7xl">
            {detailingData.heroTitle}
          </h1>
          <p className="mt-4 max-w-2xl mx-auto text-lg text-gray-300">
            {detailingData.heroSubtitle}
          </p>
          <p className="mt-2 max-w-2xl mx-auto text-base text-gray-400">
            {detailingData.heroDescription}
          </p>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-8 sm:py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-extrabold text-anthracite sm:text-4xl">Unsere Aufbereitungs-Services</h2>
            <p className="mt-4 text-lg text-gray-600">Professionelle Fahrzeugpflege für perfekte Ergebnisse</p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
            {detailingData.services.map((service, index) => {
              return (
                <div key={index} className="bg-gray-50 rounded-lg shadow-md p-6 flex flex-col text-center transition-transform hover:scale-105 hover:shadow-xl relative">
                  {/* Dezenter WhatsApp Button unten rechts */}
                  <ServiceWhatsAppButton serviceTitle={service.title} />

                  <div className="bg-accent-red text-white rounded-full p-4 mb-4 mx-auto">
                    <DetailingIcon icon={service.icon} className="w-8 h-8" />
                  </div>
                  <h3 className="text-xl font-bold text-anthracite mb-2">{service.title}</h3>
                  <p className="text-gray-600 mb-4 flex-grow">
                    {service.description}
                  </p>
                  <ul className="space-y-2 mb-6 text-left">
                    {service.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-sm text-gray-700">
                        <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                        {feature}
                      </li>
                    ))}
                  </ul>

                  <a
                    href={service.buttonLink}
                    target={service.isExternalLink ? "_blank" : undefined}
                    rel={service.isExternalLink ? "noopener noreferrer" : undefined}
                    className="font-semibold text-accent-red hover:underline"
                  >
                    {service.buttonText}
                  </a>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-anthracite text-white py-16 sm:py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-extrabold sm:text-4xl">
            {detailingData.ctaTitle}
          </h2>
          <p className="mt-4 text-lg text-gray-300">
            {detailingData.ctaDescription}
          </p>
          <div className="mt-8">
            <a
              href="mailto:<EMAIL>"
              className="flex min-w-[84px] cursor-pointer items-center justify-center overflow-hidden rounded-md h-12 px-6 bg-accent-red text-white text-base font-bold shadow-xl transition-transform hover:scale-105 mx-auto"
            >
              {detailingData.ctaButtonText}
            </a>
          </div>
        </div>
      </section>
    </div>
  );
} 