import Link from 'next/link'
import { strings } from '@/strings'
import { sanityService } from '@/sanity/services/SanityService'
import HeroSection from '@/components/HeroSection'

interface HomepageContent {
  heroTitle: string
  heroSubtitle: string
  heroDescription: string
  ctaButtonText: string
  ctaSecondaryText: string
  features: Array<{
    title: string
    description: string
    icon: string
  }>
  swipingServices?: Array<{
    text: string
    order: number
  }>
  detailedServices?: Array<{
    title: string
    order: number
    services: Array<{
      title: string
      description: string
    }>
  }>
}

export default async function HomePage() {
  // Fallback zu strings falls Sanity nicht verfügbar
  const sanityData = await sanityService.getHomepage()
  const fallbackContent: HomepageContent = {
    heroTitle: strings.home.title,
    heroSubtitle: strings.home.subtitle,
    heroDescription: strings.home.heroDescription,
    ctaButtonText: strings.home.ctaButton,
    ctaSecondaryText: strings.home.ctaSecondary,
    features: [
      {
        title: 'Leistungssteigerung',
        description: 'Bis zu 30% mehr Leistung durch professionelle Software-Optimierung',
        icon: 'lightning'
      },
      {
        title: 'Garagen-Service',
        description: 'Komplette Werkstattdienstleistungen: Ölservice, Bremsen, Getriebe und allgemeine Reparaturen',
        icon: 'cog'
      },
      {
        title: 'Schneller Service',
        description: 'Terminvereinbarung innerhalb von 24 Stunden möglich',
        icon: 'clock'
      }
    ]
  }
  const content = (sanityData as unknown as HomepageContent) || fallbackContent

  return (
    <div className="relative flex size-full min-h-screen flex-col group/design-root overflow-x-hidden">

      <div className="layout-container flex h-full grow flex-col">
        <main className="flex-1">
          {/* Hero Section */}
          <HeroSection
            heroTitle={content.heroTitle}
            heroSubtitle={content.heroSubtitle}
            heroDescription={content.heroDescription}
            swipingServices={content.swipingServices}
          />

          {/* Services Section */}
          <section className="py-8 sm:py-12" id="services">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-extrabold text-anthracite sm:text-4xl">Unsere Dienstleistungen</h2>
                <p className="mt-4 text-lg text-gray-600">Wir bieten eine breite Palette an Services, um Ihr Fahrzeug zu perfektionieren.</p>
              </div>
              <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
                <div className="bg-gray-50 rounded-lg shadow-md p-6 flex flex-col items-center text-center transition-transform hover:scale-105 hover:shadow-xl">
                  <div className="bg-accent-red text-white rounded-full p-4 mb-4">
                    <svg fill="currentColor" height="32px" viewBox="0 0 256 256" width="32px" xmlns="http://www.w3.org/2000/svg">
                      <path d="M128,80a48,48,0,1,0,48,48A48.05,48.05,0,0,0,128,80Zm0,80a32,32,0,1,1,32-32A32,32,0,0,1,128,160Zm88-29.84q.06-2.16,0-4.32l14.92-18.64a8,8,0,0,0,1.48-7.06,107.21,107.21,0,0,0-10.88-26.25,8,8,0,0,0-6-3.93l-23.72-2.64q-1.48-1.56-3-3L186,40.54a8,8,0,0,0-3.94-6,107.71,107.71,0,0,0-26.25-10.87,8,8,0,0,0-7.06,1.49L130.16,40Q128,40,125.84,40L107.2,25.11a8,8,0,0,0-7.06-1.48A107.6,107.6,0,0,0,73.89,34.51a8,8,0,0,0-3.93,6L67.32,64.27q-1.56,1.49-3,3L40.54,70a8,8,0,0,0-6,3.94,107.71,107.71,0,0,0-10.87,26.25,8,8,0,0,0,1.49,7.06L40,125.84Q40,128,40,130.16L25.11,148.8a8,8,0,0,0-1.48,7.06,107.21,107.21,0,0,0,10.88,26.25,8,8,0,0,0,6,3.93l23.72,2.64q1.49,1.56,3,3L70,215.46a8,8,0,0,0,3.94,6,107.71,107.71,0,0,0,26.25,10.87,8,8,0,0,0,7.06-1.49L125.84,216q2.16.06,4.32,0l18.64,14.92a8,8,0,0,0,7.06,1.48,107.21,107.21,0,0,0,26.25-10.88,8,8,0,0,0,3.93-6l2.64-23.72q1.56-1.48,3-3L215.46,186a8,8,0,0,0,6-3.94,107.71,107.71,0,0,0,10.87-26.25,8,8,0,0,0-1.49-7.06Zm-16.1-6.5a73.93,73.93,0,0,1,0,8.68,8,8,0,0,0,1.74,5.48l14.19,17.73a91.57,91.57,0,0,1-6.23,15L187,173.11a8,8,0,0,0-5.1,2.64,74.11,74.11,0,0,1-6.14,6.14,8,8,0,0,0-2.64,5.1l-2.51,22.58a91.32,91.32,0,0,1-15,6.23l-17.74-14.19a8,8,0,0,0-5-1.75h-.48a73.93,73.93,0,0,1-8.68,0,8,8,0,0,0-5.48,1.74L100.45,215.8a91.57,91.57,0,0,1-15-6.23L82.89,187a8,8,0,0,0-2.64-5.1,74.11,74.11,0,0,1-6.14-6.14,8,8,0,0,0-5.1-2.64L46.43,170.6a91.32,91.32,0,0,1-6.23-15l14.19-17.74a8,8,0,0,0,1.74-5.48,73.93,73.93,0,0,1,0-8.68,8,8,0,0,0-1.74-5.48L40.2,100.45a91.57,91.57,0,0,1,6.23-15L69,82.89a8,8,0,0,0,5.1-2.64,74.11,74.11,0,0,1,6.14-6.14A8,8,0,0,0,82.89,69L85.4,46.43a91.32,91.32,0,0,1,15-6.23l17.74,14.19a8,8,0,0,0,5.48,1.74,73.93,73.93,0,0,1,8.68,0,8,8,0,0,0,5.48-1.74L155.55,40.2a91.57,91.57,0,0,1,15,6.23L173.11,69a8,8,0,0,0,2.64,5.1,74.11,74.11,0,0,1,6.14,6.14,8,8,0,0,0,5.1,2.64l22.58,2.51a91.32,91.32,0,0,1,6.23,15l-14.19,17.74A8,8,0,0,0,199.87,123.66Z"></path>
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-anthracite mb-2">Software-Tuning</h3>
                  <p className="text-gray-600 mb-4">Leistungssteigerungen und Optimierungen durch professionelle Kennfeldoptimierung.</p>
                  <Link href="/tuning" className="font-semibold text-accent-red hover:underline">Mehr erfahren</Link>
                </div>
                <div className="bg-white rounded-lg shadow-lg p-6 flex flex-col items-center text-center transition-transform hover:scale-105 hover:shadow-xl border-l-4 border-anthracite">
                  <div className="bg-anthracite text-white rounded-full p-4 mb-4">
                    <svg fill="currentColor" height="32px" viewBox="0 0 256 256" width="32px" xmlns="http://www.w3.org/2000/svg">
                      <path d="M240,112H229.2L201.42,49.5A16,16,0,0,0,186.8,40H69.2a16,16,0,0,0-14.62,9.5L26.8,112H16a8,8,0,0,0,0,16h8v80a16,16,0,0,0,16,16H64a16,16,0,0,0,16-16V192h96v16a16,16,0,0,0,16,16h24a16,16,0,0,0,16-16V128h8a8,8,0,0,0,0-16ZM69.2,56H186.8l24.89,56H44.31ZM64,208H40V192H64Zm128,0V192h24v16Zm24-32H40V128H216ZM56,152a8,8,0,0,1,8-8H80a8,8,0,0,1,0,16H64A8,8,0,0,1,56,152Zm112,0a8,8,0,0,1,8-8h16a8,8,0,0,1,0,16H176A8,8,0,0,1,168,152Z"></path>
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-anthracite mb-2">Fahrzeugaufbereitung</h3>
                  <p className="text-gray-600 mb-4">Umfassende Reinigung und Wiederherstellung für perfekten Glanz Ihres Fahrzeugs.</p>
                  <Link href="/fahrzeugaufbereitung" className="font-semibold text-accent-red hover:underline">Mehr erfahren</Link>
                </div>
                <div className="bg-white rounded-lg shadow-lg p-6 flex flex-col items-center text-center transition-transform hover:scale-105 hover:shadow-xl border-l-4 border-accent-red">
                  <div className="bg-accent-red text-white rounded-full p-4 mb-4">
                    <svg fill="currentColor" height="32px" viewBox="0 0 256 256" width="32px" xmlns="http://www.w3.org/2000/svg">
                      <path d="M128,80a48,48,0,1,0,48,48A48.05,48.05,0,0,0,128,80Zm0,80a32,32,0,1,1,32-32A32,32,0,0,1,128,160Zm88-29.84q.06-2.16,0-4.32l14.92-18.64a8,8,0,0,0,1.48-7.06,107.6,107.6,0,0,0-10.88-26.25,8,8,0,0,0-6-3.93l-23.72-2.64q-1.48-1.56-3.06-3.05L221.4,40.5a8,8,0,0,0-3.94-6,107.29,107.29,0,0,0-26.25-10.87,8,8,0,0,0-7.06,1.49L165.51,40Q163.35,40,161.19,40l-18.64-14.92a8,8,0,0,0-7.06-1.48A107.6,107.6,0,0,0,109.24,34.5a8,8,0,0,0-3.93,6L102.67,64.27q-1.56,1.49-3.05,3.06L75.89,64.6a8,8,0,0,0-6,3.94A107.71,107.71,0,0,0,59,94.79a8,8,0,0,0,1.49,7.06L75.41,120.49q0,2.16,0,4.32L60.49,143.45a8,8,0,0,0-1.48,7.06,107.6,107.6,0,0,0,10.88,26.25,8,8,0,0,0,6,3.93l23.72,2.64q1.49,1.56,3.06,3.05L99.4,215.5a8,8,0,0,0,3.94,6,107.71,107.71,0,0,0,26.25,10.87,8,8,0,0,0,7.06-1.49L155.29,216q2.16.06,4.32,0l18.64,14.92a8,8,0,0,0,7.06,1.48,107.21,107.21,0,0,0,26.25-10.88,8,8,0,0,0,3.93-6l2.64-23.72q1.56-1.48,3.05-3.06L244.11,191.4a8,8,0,0,0,6-3.94,107.71,107.71,0,0,0,10.87-26.25,8,8,0,0,0-1.49-7.06ZM128,192a64,64,0,1,1,64-64A64.07,64.07,0,0,1,128,192Z"></path>
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-anthracite mb-2">Garagen-Service</h3>
                  <p className="text-gray-600 mb-4">Komplette Werkstattdienstleistungen: Ölservice, Bremsen, Getriebe und allgemeine Reparaturen.</p>
                  <Link href="/services" className="font-semibold text-accent-red hover:underline">Mehr erfahren</Link>
                </div>
              </div>
            </div>
          </section>

          {/* Detailed Services Section */}
          <section className="bg-white py-8 sm:py-12">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-left mb-12">
                <h2 className="text-3xl font-extrabold text-anthracite sm:text-4xl">Detaillierte Dienstleistungen</h2>
                <p className="mt-4 text-lg text-gray-600">Entdecken Sie unsere spezifischen Angebote in jedem Bereich.</p>
              </div>
              <div className="space-y-12">
                {content.detailedServices && content.detailedServices.length > 0 ? (
                  content.detailedServices.map((section, index) => (
                    <div key={index}>
                      <h3 className="text-2xl font-bold text-anthracite mb-6 border-l-4 border-accent-red pl-4">{section.title}</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                        {section.services.map((service, serviceIndex) => (
                          <div key={serviceIndex} className="bg-gray-50 rounded-lg p-6 border-l-4 border-gray-200 hover:border-accent-red transition-colors">
                            <h4 className="font-bold text-lg text-anthracite mb-2">{service.title}</h4>
                            <p className="text-gray-600">{service.description}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))
                ) : (
                  // Fallback content if no Sanity data available
                  <>
                    <div>
                      <h3 className="text-2xl font-bold text-anthracite mb-6 border-l-4 border-accent-red pl-4">Software-Tuning</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                        <div className="bg-gray-50 rounded-lg p-6 border-l-4 border-gray-200 hover:border-accent-red transition-colors">
                          <h4 className="font-bold text-lg text-anthracite mb-2">Kennfeldoptimierung</h4>
                          <p className="text-gray-600">Professionelle Optimierung der Motorsteuerung für mehr Leistung und Effizienz.</p>
                        </div>
                        <div className="bg-gray-50 rounded-lg p-6 border-l-4 border-gray-200 hover:border-accent-red transition-colors">
                          <h4 className="font-bold text-lg text-anthracite mb-2">Leistungssteigerung</h4>
                          <p className="text-gray-600">Bis zu 30% mehr Leistung und Drehmoment durch professionelle Software-Optimierung.</p>
                        </div>
                        <div className="bg-gray-50 rounded-lg p-6 border-l-4 border-gray-200 hover:border-accent-red transition-colors">
                          <h4 className="font-bold text-lg text-anthracite mb-2">Kraftstoffoptimierung</h4>
                          <p className="text-gray-600">Verbesserter Kraftstoffverbrauch bei gleichzeitig gesteigerter Leistung.</p>
                        </div>
                      </div>
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-anthracite mb-6 border-l-4 border-accent-red pl-4">Fahrzeugaufbereitung</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                        <div className="bg-gray-50 rounded-lg p-6 border-l-4 border-gray-200 hover:border-accent-red transition-colors">
                          <h4 className="font-bold text-lg text-anthracite mb-2">Lackpolitur</h4>
                          <p className="text-gray-600">Professionelle Entfernung von Kratzern und Hologrammen für perfekte Lacktiefe.</p>
                        </div>
                        <div className="bg-gray-50 rounded-lg p-6 border-l-4 border-gray-200 hover:border-accent-red transition-colors">
                          <h4 className="font-bold text-lg text-anthracite mb-2">Keramik-Versiegelung</h4>
                          <p className="text-gray-600">Langanhaltender Schutz mit hochwertiger Keramikbeschichtung für bis zu 5 Jahre.</p>
                        </div>
                        <div className="bg-gray-50 rounded-lg p-6 border-l-4 border-gray-200 hover:border-accent-red transition-colors">
                          <h4 className="font-bold text-lg text-anthracite mb-2">Innenreinigung</h4>
                          <p className="text-gray-600">Gründliche Reinigung und Aufbereitung des Fahrzeuginnenraums.</p>
                        </div>
                      </div>
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-anthracite mb-6 border-l-4 border-accent-red pl-4">Garagen-Service</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                        <div className="bg-gray-50 rounded-lg p-6 border-l-4 border-gray-200 hover:border-accent-red transition-colors">
                          <h4 className="font-bold text-lg text-anthracite mb-2">Ölservice</h4>
                          <p className="text-gray-600">Professioneller Motorölwechsel mit hochwertigen Ölen und Originalfiltern für optimale Motorleistung.</p>
                        </div>
                        <div className="bg-gray-50 rounded-lg p-6 border-l-4 border-gray-200 hover:border-accent-red transition-colors">
                          <h4 className="font-bold text-lg text-anthracite mb-2">Getriebeservice</h4>
                          <p className="text-gray-600">Wartung und Reparatur von Schalt- und Automatikgetrieben für reibungslose Kraftübertragung.</p>
                        </div>
                        <div className="bg-gray-50 rounded-lg p-6 border-l-4 border-gray-200 hover:border-accent-red transition-colors">
                          <h4 className="font-bold text-lg text-anthracite mb-2">Bremsenservice</h4>
                          <p className="text-gray-600">Kontrolle und Austausch von Bremsbelägen, Bremsscheiben und Bremsflüssigkeit für Ihre Sicherheit.</p>
                        </div>
                        <div className="bg-gray-50 rounded-lg p-6 border-l-4 border-gray-200 hover:border-accent-red transition-colors">
                          <h4 className="font-bold text-lg text-anthracite mb-2">Allgemeine Reparaturen</h4>
                          <p className="text-gray-600">Diagnose und Reparatur von Motorproblemen, Elektronik und anderen Fahrzeugkomponenten.</p>
                        </div>
                        <div className="bg-gray-50 rounded-lg p-6 border-l-4 border-gray-200 hover:border-accent-red transition-colors">
                          <h4 className="font-bold text-lg text-anthracite mb-2">Inspektion & Wartung</h4>
                          <p className="text-gray-600">Regelmässige Fahrzeuginspektion und vorbeugende Wartung nach Herstellervorgaben.</p>
                        </div>
                        <div className="bg-gray-50 rounded-lg p-6 border-l-4 border-gray-200 hover:border-accent-red transition-colors">
                          <h4 className="font-bold text-lg text-anthracite mb-2">Klimaanlagen-Service</h4>
                          <p className="text-gray-600">Wartung, Befüllung und Reparatur von Klimaanlagen für optimalen Fahrkomfort.</p>
                        </div>
                      </div>
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-anthracite mb-6 border-l-4 border-accent-red pl-4">Service & Garantie</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                        <div className="bg-gray-50 rounded-lg p-6 border-l-4 border-gray-200 hover:border-accent-red transition-colors">
                          <h4 className="font-bold text-lg text-anthracite mb-2">Zertifizierte Techniker</h4>
                          <p className="text-gray-600">Ausgebildete Fachkräfte mit jahrelanger Erfahrung in der Fahrzeugoptimierung.</p>
                        </div>
                        <div className="bg-gray-50 rounded-lg p-6 border-l-4 border-gray-200 hover:border-accent-red transition-colors">
                          <h4 className="font-bold text-lg text-anthracite mb-2">Modernste Ausrüstung</h4>
                          <p className="text-gray-600">Neueste Technologie und professionelle Werkzeuge für beste Ergebnisse.</p>
                        </div>
                        <div className="bg-gray-50 rounded-lg p-6 border-l-4 border-gray-200 hover:border-accent-red transition-colors">
                          <h4 className="font-bold text-lg text-anthracite mb-2">Schneller Service</h4>
                          <p className="text-gray-600">Terminvereinbarung innerhalb von 24 Stunden und zügige Bearbeitung.</p>
                        </div>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </div>
          </section>

          {/* CTA Section */}
          <section className="bg-anthracite text-white py-16 sm:py-24">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
              <h2 className="text-3xl font-extrabold sm:text-4xl">Bereit für eine Leistungssteigerung?</h2>
              <p className="mt-4 text-lg text-gray-300">Kontaktieren Sie uns noch heute, um einen Termin zu vereinbaren und Ihr Fahrzeug auf das nächste Level zu heben.</p>
              <button className="mt-8 flex mx-auto min-w-[84px] cursor-pointer items-center justify-center overflow-hidden rounded-md h-12 px-6 bg-accent-red text-white text-base font-bold shadow-xl transition-transform hover:scale-105">
                <Link href="/tuning" className="truncate">{content.ctaButtonText}</Link>
              </button>
            </div>
          </section>
        </main>
      </div>
    </div>
  )
}