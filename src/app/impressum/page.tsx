import { sanityService } from '@/sanity/services/SanityService'
import { PortableText } from '@portabletext/react'

interface LegalPageData {
  title: string
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  content: any[]
}

async function getLegalPage(slug: string): Promise<LegalPageData | null> {
  return sanityService.getLegalPage(slug) as Promise<LegalPageData | null>
}

export default async function ImpressumPage() {
  const data = await getLegalPage('impressum')
  
  if (!data) {
    return (
      <div className="container mx-auto px-6 py-16">
        <h1 className="text-3xl font-bold mb-8">Seite nicht gefunden</h1>
        <p>Die angeforderte Seite konnte nicht gefunden werden.</p>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-6 py-16">
      <h1 className="text-3xl font-bold mb-8">{data.title}</h1>
      <div className="prose prose-lg max-w-none">
        <PortableText value={data.content} />
      </div>
    </div>
  )
}

export async function generateMetadata() {
  const data = await getLegalPage('impressum')
  
  return {
    title: data?.title || 'Impressum',
    description: 'Impressum von DB-Performance Garage Bytyci',
  }
} 