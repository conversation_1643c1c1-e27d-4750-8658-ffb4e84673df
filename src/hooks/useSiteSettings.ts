'use client'

import { useState, useEffect } from 'react'
import { sanityService } from '@/sanity/services/SanityService'
import type { SiteSettingsQueryResult } from '@/sanity/types'

export const useSiteSettings = () => {
  const [siteSettings, setSiteSettings] = useState<SiteSettingsQueryResult | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchSiteSettings = async () => {
      try {
        setLoading(true)
        const settings = await sanityService.getSiteSettings()
        setSiteSettings(settings)
        setError(null)
      } catch (err) {
        console.error('Error fetching site settings:', err)
        setError('Failed to load site settings')
      } finally {
        setLoading(false)
      }
    }

    fetchSiteSettings()
  }, [])

  return {
    siteSettings,
    loading,
    error,
    whatsappNumber: siteSettings?.footer?.whatsapp,
  }
} 