/* Swiss Auto Care Theme Styles */
@import url('https://fonts.googleapis.com/css2?display=swap&family=Manrope:wght@400;500;700;800&family=Noto+Sans:wght@400;500;700;900');

:root {
  --dark-gray: #22223B;
  --anthracite: #3A3A3A;
  --accent-red: #E63946;
}

/* Global font family */
body {
  font-family: Manrope, "Noto Sans", sans-serif;
}

/* Custom utility classes */
.hover-bg-accent-red:hover {
  background-color: var(--accent-red);
}

.hover-text-accent-red:hover {
  color: var(--accent-red);
}

.bg-dark-gray {
  background-color: var(--dark-gray);
}

.text-dark-gray {
  color: var(--dark-gray);
}

.bg-anthracite {
  background-color: var(--anthracite);
}

.text-anthracite {
  color: var(--anthracite);
}

.bg-accent-red {
  background-color: var(--accent-red);
}

.text-accent-red {
  color: var(--accent-red);
}

.border-accent-red {
  border-color: var(--accent-red);
}

/* Hero section background */
.hero-bg {
  background-image: linear-gradient(rgba(34, 34, 59, 0.7) 0%, rgba(34, 34, 59, 0.9) 100%), 
                    url("https://images.unsplash.com/photo-1492144534655-ae79c964c9d7?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

/* Button animations */
.btn-scale:hover {
  transform: scale(1.05);
}

/* Card hover effects */
.card-hover:hover {
  transform: scale(1.05);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Border accent animation */
.border-accent-hover:hover {
  border-color: var(--accent-red);
}

/* Smooth transitions */
.transition-all {
  transition: all 0.3s ease;
}

.transition-colors {
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
}

.transition-transform {
  transition: transform 0.3s ease;
}

/* Typography improvements */
.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Custom shadows */
.shadow-xl-custom {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Logo styling for crisp rendering */
.logo-crisp {
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  image-rendering: pixelated;
  -ms-interpolation-mode: nearest-neighbor;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .hero-bg {
    background-attachment: scroll;
  }
}
