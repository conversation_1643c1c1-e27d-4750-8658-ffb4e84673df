import { sanityService } from '@/sanity/services/SanityService'
import { ConditionalLayout } from './ConditionalLayout'
import type { SiteSettingsQueryResult } from '@/sanity/types'

interface ServerLayoutProps {
  children: React.ReactNode
}

// Simple helper to convert null to undefined
const toUndefined = (value: string | null): string | undefined => {
  return value ?? undefined
}

// Convert API response to component-compatible types
const convertSiteSettingsData = (siteSettings: SiteSettingsQueryResult) => {
  if (!siteSettings) {
    return { navigationData: undefined, footerData: undefined, logoData: undefined }
  }

  const navigationData = siteSettings.navigation ? {
    homeLabel: toUndefined(siteSettings.navigation.homeLabel),
    servicesLabel: toUndefined(siteSettings.navigation.servicesLabel),
    tuningLabel: toUndefined(siteSettings.navigation.tuningLabel),
    contactLabel: toUndefined(siteSettings.navigation.contactLabel),
  } : undefined

  const footerData = siteSettings.footer ? {
    companyName: toUndefined(siteSettings.footer.companyName),
    address: toUndefined(siteSettings.footer.address),
    rightsText: toUndefined(siteSettings.footer.rightsText),
    legalSectionTitle: toUndefined(siteSettings.footer.legalSectionTitle),
    contactSectionTitle: toUndefined(siteSettings.footer.contactSectionTitle),
    phone: toUndefined(siteSettings.footer.phone),
    email: toUndefined(siteSettings.footer.email),
    legalLinks: siteSettings.footer.legalLinks ? {
      imprintText: toUndefined(siteSettings.footer.legalLinks.imprintText),
      privacyText: toUndefined(siteSettings.footer.legalLinks.privacyText),
      termsText: toUndefined(siteSettings.footer.legalLinks.termsText),
    } : undefined,
  } : undefined

  const logoData = {
    logo: siteSettings.logo ?? null,
    siteName: toUndefined(siteSettings.siteName),
  }

  return { navigationData, footerData, logoData }
}

export const ServerLayout: React.FC<ServerLayoutProps> = async ({ children }) => {
  try {
    const siteSettings = await sanityService.getSiteSettings()
    const { navigationData, footerData, logoData } = convertSiteSettingsData(siteSettings)

    return (
      <ConditionalLayout 
        navigationData={navigationData} 
        footerData={footerData} 
        logoData={logoData}
      >
        {children}
      </ConditionalLayout>
    )
  } catch (error) {
    console.error('Error fetching site settings server-side:', error)
    // Components will fall back to default strings
    return (
      <ConditionalLayout>
        {children}
      </ConditionalLayout>
    )
  }
} 