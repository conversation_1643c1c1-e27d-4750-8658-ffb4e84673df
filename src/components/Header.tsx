'use client'

import Link from 'next/link'
import Image from 'next/image'
import { strings } from '@/strings'
import { useState } from 'react'
import { WhatsAppButton } from './WhatsAppButton'

interface NavigationData {
  homeLabel?: string
  servicesLabel?: string
  tuningLabel?: string
  detailingLabel?: string
  contactLabel?: string
}

interface LogoData {
  logo?: {
    asset?: {
      _ref: string
      _type: 'reference'
    }
    hotspot?: {
      x?: number
      y?: number
      height?: number
      width?: number
    }
    crop?: {
      top?: number
      bottom?: number
      left?: number
      right?: number
    }
    _type: 'image'
  } | null
  siteName?: string | null
}

interface HeaderProps {
  navigationData?: NavigationData | null
  logoData?: LogoData | null
}

export const Header: React.FC<HeaderProps> = ({ navigationData, logoData }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  // Fallback to strings if no data provided
  const getNavigationContent = () => {
    if (!navigationData) {
      return {
        homeLabel: strings.nav.home,
        servicesLabel: strings.nav.services,
        tuningLabel: strings.nav.tuning,
        detailingLabel: strings.nav.detailing,
        contactLabel: strings.nav.contact,
      }
    }
    return navigationData
  }

  const nav = getNavigationContent()
  const siteName = logoData?.siteName || 'DB-Performance'

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  return (
    <div className="relative">
      <header className="bg-white text-dark-gray flex items-center justify-between whitespace-nowrap px-4 md:px-10 py-4 shadow-md">
          <Link href="/" className="flex items-center hover:opacity-80 transition-opacity">
            <Image
              src="/logo.svg"
              alt={siteName}
              width={200}
              height={100}
              className="h-12 w-auto logo-crisp"
              priority
              style={{
                imageRendering: 'auto',
                WebkitFontSmoothing: 'antialiased',
                MozOsxFontSmoothing: 'grayscale'
              }}
            />
          </Link>
          <nav className="flex flex-1 justify-end items-center gap-6">
            {/* Desktop Navigation - Hidden on mobile */}
            <div className="hidden md:flex items-center gap-6">
              <Link href="/" className="text-base font-bold transition-colors text-anthracite hover-text-accent-red">{nav.homeLabel}</Link>
              <Link href="/services" className="text-base font-bold transition-colors text-anthracite hover-text-accent-red">{nav.servicesLabel}</Link>
              <Link href="/tuning" className="text-base font-bold transition-colors text-anthracite hover-text-accent-red">{nav.tuningLabel}</Link>
              <Link href="/fahrzeugaufbereitung" className="text-base font-bold transition-colors text-anthracite hover-text-accent-red">{nav.detailingLabel}</Link>
              <Link href="/contact" className="text-base font-bold transition-colors text-anthracite hover-text-accent-red">{nav.contactLabel}</Link>
              <button className="flex min-w-[84px] cursor-pointer items-center justify-center overflow-hidden rounded-md h-10 px-4 bg-accent-red text-white text-base font-bold shadow-lg transition-transform hover:scale-105">
                <Link href="/tuning" className="truncate">Konfigurator starten</Link>
              </button>
            </div>

            {/* Mobile Menu Button - Only visible on mobile */}
            <button
              onClick={toggleMenu}
              className="md:hidden p-2 rounded-md text-dark-gray hover:text-accent-red transition-colors"
              aria-label="Toggle menu"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </nav>
        </header>

        {/* Mobile Navigation - Positioned absolutely below header */}
        <div className={`md:hidden absolute top-full left-0 right-0 bg-white shadow-lg z-50 transition-all duration-300 ${isMenuOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0 overflow-hidden'}`}>
          <div className="py-4 space-y-4 border-t border-gray-200">
            <Link
              href="/"
              className="block text-anthracite hover:text-accent-red transition-colors font-bold text-base px-6"
              onClick={() => setIsMenuOpen(false)}
            >
              {nav.homeLabel}
            </Link>
            <Link
              href="/services"
              className="block text-anthracite hover:text-accent-red transition-colors font-bold text-base px-6"
              onClick={() => setIsMenuOpen(false)}
            >
              {nav.servicesLabel}
            </Link>
            <Link
              href="/tuning"
              className="block text-anthracite hover:text-accent-red transition-colors font-bold text-base px-6"
              onClick={() => setIsMenuOpen(false)}
            >
              {nav.tuningLabel}
            </Link>
            <Link
              href="/fahrzeugaufbereitung"
              className="block text-anthracite hover:text-accent-red transition-colors font-bold text-base px-6"
              onClick={() => setIsMenuOpen(false)}
            >
              {nav.detailingLabel}
            </Link>
            <Link
              href="/contact"
              className="block text-anthracite hover:text-accent-red transition-colors font-bold text-base px-6"
              onClick={() => setIsMenuOpen(false)}
            >
              {nav.contactLabel}
            </Link>

            {/* Mobile Konfigurator Button */}
            <div className="px-6 pt-2">
              <Link
                href="/tuning"
                className="block w-full text-center bg-accent-red text-white py-3 px-4 rounded-md font-bold text-base hover:bg-red-700 transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                Konfigurator starten
              </Link>
            </div>

            {/* Mobile Contact Buttons */}
            <div className="px-6 pb-2">
              <div onClick={() => setIsMenuOpen(false)}>
                <WhatsAppButton
                  className="w-full bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors font-medium text-center justify-center"
                  message={strings.contact.whatsapp.defaultMessage}
                />
              </div>
            </div>
          </div>
        </div>
    </div>
  )
} 