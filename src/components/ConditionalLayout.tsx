'use client'

import { usePathname } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Header } from './Header'
import { Footer } from './Footer'
import { sanityService } from '@/sanity/services/SanityService'

interface NavigationData {
  homeLabel?: string
  servicesLabel?: string
  tuningLabel?: string
  contactLabel?: string
}

interface FooterData {
  companyName?: string
  address?: string
  rightsText?: string
  legalSectionTitle?: string
  contactSectionTitle?: string
  phone?: string
  email?: string
  legalLinks?: {
    imprintText?: string
    privacyText?: string
    termsText?: string
  }
}

interface LogoData {
  logo?: {
    asset?: {
      _ref: string
      _type: 'reference'
    }
    hotspot?: {
      x?: number
      y?: number
      height?: number
      width?: number
    }
    crop?: {
      top?: number
      bottom?: number
      left?: number
      right?: number
    }
    _type: 'image'
  } | null
  siteName?: string | null
}

interface SiteSettings {
  navigation?: NavigationData | null
  footer?: FooterData | null
  logo?: LogoData | null
}

interface ConditionalLayoutProps {
  children: React.ReactNode
  navigationData?: NavigationData | null
  footerData?: FooterData | null
  logoData?: LogoData | null
}

export const ConditionalLayout: React.FC<ConditionalLayoutProps> = ({ children, navigationData, footerData, logoData }) => {
  const pathname = usePathname()
  const isStudio = pathname?.startsWith('/studio')
  const isTuning = pathname?.startsWith('/tuning')

  const [siteSettings, setSiteSettings] = useState<SiteSettings | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function loadSiteSettings() {
      try {
        const settings = await sanityService.getSiteSettings()
        setSiteSettings(settings)
      } catch (error) {
        console.error('Error loading site settings:', error)
      } finally {
        setLoading(false)
      }
    }

    if (!isStudio) {
      loadSiteSettings()
    } else {
      setLoading(false)
    }
  }, [isStudio])

  if (isStudio) {
    // Studio gets full screen without header/footer
    return <>{children}</>
  }

  // Show loading state briefly
  if (loading) {
    return (
      <div className="flex min-h-screen flex-col">
        <div className="h-16 bg-white shadow-md"></div>
        <main className="flex-1">{children}</main>
      </div>
    )
  }

  // Use loaded site settings or fallback to props
  const finalLogoData = siteSettings?.logo || logoData
  const finalNavigationData = siteSettings?.navigation || navigationData
  const finalFooterData = siteSettings?.footer || footerData

  // Regular pages get header and footer
  return (
    <div className="flex min-h-screen flex-col">
      <Header navigationData={finalNavigationData} logoData={finalLogoData} />
      <main className={isTuning ? "" : "flex-1"}>{children}</main>
      <Footer footerData={finalFooterData} />
    </div>
  )
}