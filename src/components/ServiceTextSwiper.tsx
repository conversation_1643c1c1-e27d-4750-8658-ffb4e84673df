'use client'

import { useState, useEffect } from 'react'

interface SwipingService {
  text: string
  order: number
}

interface ServiceTextSwiperProps {
  services: SwipingService[]
  interval?: number // Milliseconds between transitions
  className?: string
  onServiceChange?: (index: number) => void // Callback when service changes
  currentIndex?: number // External control of current index
}

export default function ServiceTextSwiper({
  services,
  interval = 3500, // Schneller: 3.5 Sekunden
  className = '',
  onServiceChange,
  currentIndex: externalIndex
}: ServiceTextSwiperProps) {
  const [internalIndex, setInternalIndex] = useState(0)
  const [isVisible, setIsVisible] = useState(true)

  // Sort services by order
  const sortedServices = [...services].sort((a, b) => a.order - b.order)

  // Use external index if provided, otherwise use internal
  const currentIndex = externalIndex !== undefined ? externalIndex : internalIndex

  // Handle external index changes
  useEffect(() => {
    if (externalIndex !== undefined && externalIndex !== internalIndex) {
      setIsVisible(false)
      setTimeout(() => {
        setInternalIndex(externalIndex)
        setIsVisible(true)
      }, 400)
    }
  }, [externalIndex, internalIndex])

  useEffect(() => {
    if (sortedServices.length <= 1) return

    const timer = setInterval(() => {
      // Only auto-advance if not externally controlled
      if (externalIndex === undefined) {
        // Fade out
        setIsVisible(false)

        // After fade out, change text and fade in
        setTimeout(() => {
          const newIndex = (internalIndex + 1) % sortedServices.length
          setInternalIndex(newIndex)
          setIsVisible(true)
          // Notify parent component about the change
          onServiceChange?.(newIndex)
        }, 400) // Half of transition duration
      }
    }, interval)

    return () => clearInterval(timer)
  }, [internalIndex, sortedServices.length, interval, onServiceChange, externalIndex])

  // Notify parent on initial load and index changes
  useEffect(() => {
    onServiceChange?.(currentIndex)
  }, [currentIndex, onServiceChange])

  if (!sortedServices.length) {
    return <span className={className}>Professionelle Dienstleistungen auf höchstem Niveau</span>
  }

  const currentService = sortedServices[currentIndex]

  return (
    <div className={`w-full ${className}`} style={{ minHeight: '3em' }}>
      <div
        className={`text-center leading-tight px-4 transition-all duration-500 ease-in-out transform ${
          isVisible
            ? 'opacity-100 translate-x-0'
            : 'opacity-0 translate-x-4'
        }`}
      >
        {currentService?.text || 'Professionelle Dienstleistungen auf höchstem Niveau'}
      </div>
    </div>
  )
}
