'use client';

import { useState, useEffect } from 'react';

interface TuningStage {
  name: string;
  stage_number: number;
  raw_table_text: string;
  power_original: string;
  power_tuned: string;
  power_difference: string;
  price?: string;
  price_uvp?: string;
  torque_original: string;
  torque_tuned: string;
  torque_difference: string;
}

interface Vehicle {
  vehicle_type: string;
  brand: string;
  model: string;
  year: string;
  motor: string;
  scraped_at: string;
  country: string;
  stages: TuningStage[];
  total_stages: number;
}

// API Response interfaces
interface VehicleTypeResponse {
  vehicle_type: string;
}

interface BrandResponse {
  brand: string;
}

interface ModelResponse {
  model: string;
}

interface YearResponse {
  year: string;
}

interface MotorResponse {
  motor: string;
}

interface CountResponse {
  count: number;
}

const TuningConfigurator: React.FC = () => {
  const [totalVehicles, setTotalVehicles] = useState<number>(0);
  const [loading, setLoading] = useState(true);
  const [selectedVehicleType, setSelectedVehicleType] = useState<string>('');
  const [selectedBrand, setSelectedBrand] = useState<string>('');
  const [selectedModel, setSelectedModel] = useState<string>('');
  const [selectedYear, setSelectedYear] = useState<string>('');
  const [selectedMotor, setSelectedMotor] = useState<string>('');
  const [selectedVehicle, setSelectedVehicle] = useState<Vehicle | null>(null);
  
  // Dynamic filter data
  const [vehicleTypes, setVehicleTypes] = useState<string[]>([]);
  const [brands, setBrands] = useState<string[]>([]);
  const [models, setModels] = useState<string[]>([]);
  const [years, setYears] = useState<string[]>([]);
  const [motors, setMotors] = useState<string[]>([]);

  // Load initial data
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        setLoading(true);
        
        // Only load vehicle types initially for faster startup
        const typesResponse = await fetch('/api/configurator?types=true');
        const typesData: VehicleTypeResponse[] = await typesResponse.json();
        
        if (typesResponse.ok) {
          // Extract and deduplicate vehicle types
          const extractedTypes = typesData.map((item) => item.vehicle_type);
          const uniqueTypes = [...new Set(extractedTypes)].filter(Boolean); // Remove duplicates and empty values
          
          setVehicleTypes(uniqueTypes);
          
          // Set first available type as default (should be PKW now)
          if (uniqueTypes.length > 0 && !selectedVehicleType) {
            setSelectedVehicleType(uniqueTypes[0]);
          }
        } else {
          console.warn('Failed to load vehicle types - Supabase may not be running');
          // Set fallback data when Supabase is not available
          setVehicleTypes(['Kraftfahrzeuge (PKW)', 'Motorräder/Quads/Roller', 'Motorboote']);
          setSelectedVehicleType('Kraftfahrzeuge (PKW)');
        }
      } catch (error) {
        console.warn('Error loading initial data - using fallback data:', error);
        // Set fallback data when there's an error
        setVehicleTypes(['Kraftfahrzeuge (PKW)', 'Motorräder/Quads/Roller', 'Motorboote']);
        setSelectedVehicleType('Kraftfahrzeuge (PKW)');
      } finally {
        setLoading(false);
      }
    };

    loadInitialData();
  }, [selectedVehicleType]); // Only depend on selectedVehicleType to avoid re-running

  // Load count and brands when vehicle type changes
  useEffect(() => {
    const loadVehicleTypeData = async () => {
      if (!selectedVehicleType) return;
      
      try {
        // Load total count for selected vehicle type
        const countResponse = await fetch(`/api/configurator?type=${encodeURIComponent(selectedVehicleType)}&count=true`);
        const countData: CountResponse = await countResponse.json();
        
        if (countResponse.ok) {
          setTotalVehicles(countData.count || 0);
        }

        // Load brands for the selected vehicle type
        const brandsResponse = await fetch(`/api/configurator?type=${encodeURIComponent(selectedVehicleType)}&brands=true`);
        if (brandsResponse.ok) {
          const brandsData: BrandResponse[] = await brandsResponse.json();
          const extractedBrands = brandsData.map((item) => item.brand);
          const uniqueBrands = [...new Set(extractedBrands)].filter(Boolean);
          
          setBrands(uniqueBrands);
          
          // Reset subsequent selections when vehicle type changes
          setSelectedBrand('');
          setModels([]);
          setYears([]);
          setMotors([]);
          setSelectedModel('');
          setSelectedYear('');
          setSelectedMotor('');
          setSelectedVehicle(null);
        }
      } catch (error) {
        console.error('Error loading vehicle type data:', error);
      }
    };

    loadVehicleTypeData();
  }, [selectedVehicleType]);

  // Load models when brand changes
  useEffect(() => {
    const loadModels = async () => {
      if (!selectedVehicleType || !selectedBrand) {
        setModels([]);
        return;
      }
      
      try {
        const response = await fetch('/api/configurator', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ 
            action: 'models',
            vehicleType: selectedVehicleType,
            brand: selectedBrand
          })
        });
        const data: ModelResponse[] = await response.json();
        
        if (response.ok) {
          setModels(data.map((item) => item.model));
          
                     // Reset subsequent selections when brand changes
           setSelectedModel('');
           setYears([]);
           setMotors([]);
           setSelectedYear('');
           setSelectedMotor('');
           setSelectedVehicle(null);
        } else {
          console.error('Error loading models:', (data as { error?: string }).error);
          setModels([]);
        }
      } catch (error) {
        console.error('Error loading models:', error);
        setModels([]);
      }
    };

    loadModels();
  }, [selectedVehicleType, selectedBrand]);

  // Load years when model changes
  useEffect(() => {
    const loadYears = async () => {
      if (!selectedVehicleType || !selectedBrand || !selectedModel) {
        setYears([]);
        return;
      }
      
      try {
        const response = await fetch('/api/configurator', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ 
            action: 'years',
            vehicleType: selectedVehicleType,
            brand: selectedBrand,
            model: selectedModel
          })
        });
        const data: YearResponse[] = await response.json();
        
        if (response.ok) {
          setYears(data.map((item) => item.year));
          
          // Reset subsequent selections when model changes
          setSelectedYear('');
          setMotors([]);
          setSelectedMotor('');
          setSelectedVehicle(null);
        } else {
          console.error('Error loading years:', (data as { error?: string }).error);
          setYears([]);
        }
      } catch (error) {
        console.error('Error loading years:', error);
        setYears([]);
      }
    };

    loadYears();
  }, [selectedVehicleType, selectedBrand, selectedModel]);

  // Load motors when year changes
  useEffect(() => {
    const loadMotors = async () => {
      if (!selectedVehicleType || !selectedBrand || !selectedModel || !selectedYear) {
        setMotors([]);
        return;
      }
      
      try {
        const response = await fetch('/api/configurator', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ 
            action: 'motors',
            vehicleType: selectedVehicleType,
            brand: selectedBrand,
            model: selectedModel,
            year: selectedYear
          })
        });
        const data: MotorResponse[] = await response.json();
        
        if (response.ok) {
          setMotors(data.map((item) => item.motor));
          
          // Reset subsequent selections when year changes
          setSelectedMotor('');
          setSelectedVehicle(null);
        } else {
          console.error('Error loading motors:', (data as { error?: string }).error);
          setMotors([]);
        }
      } catch (error) {
        console.error('Error loading motors:', error);
        setMotors([]);
      }
    };

    loadMotors();
  }, [selectedVehicleType, selectedBrand, selectedModel, selectedYear]);

  // Handle selections
  const handleVehicleTypeChange = (type: string) => {
    setSelectedVehicleType(type);
    setSelectedBrand('');
    setSelectedModel('');
    setSelectedYear('');
    setSelectedMotor('');
    setSelectedVehicle(null);
    // Reset dependent filter data
    setBrands([]);
    setModels([]);
    setYears([]);
    setMotors([]);
  };

  const handleBrandChange = (brand: string) => {
    setSelectedBrand(brand);
    setSelectedModel('');
    setSelectedYear('');
    setSelectedMotor('');
    setSelectedVehicle(null);
    // Reset dependent filter data
    setModels([]);
    setYears([]);
    setMotors([]);
  };

  const handleModelChange = (model: string) => {
    setSelectedModel(model);
    setSelectedYear('');
    setSelectedMotor('');
    setSelectedVehicle(null);
    // Reset dependent filter data
    setYears([]);
    setMotors([]);
  };

  const handleYearChange = (year: string) => {
    setSelectedYear(year);
    setSelectedMotor('');
    setSelectedVehicle(null);
    // Reset dependent filter data
    setMotors([]);
  };

  const handleMotorChange = async (motor: string) => {
    setSelectedMotor(motor);
    
    // Load the complete vehicle data
    try {
      const response = await fetch(`/api/configurator?type=${encodeURIComponent(selectedVehicleType)}&brand=${encodeURIComponent(selectedBrand)}&model=${encodeURIComponent(selectedModel)}&year=${encodeURIComponent(selectedYear)}&motor=${encodeURIComponent(motor)}`);
      const data = await response.json();
      
      if (response.ok && data.length > 0) {
        setSelectedVehicle(data[0]);
      } else {
        console.error('Error loading vehicle data:', data.error);
        setSelectedVehicle(null);
      }
    } catch (error) {
      console.error('Error loading vehicle data:', error);
      setSelectedVehicle(null);
    }
  };

  const formatPrice = (price?: string) => {
    // Use the CHF price from database (already converted and includes minimum pricing)
    if (price && price.includes('CHF')) {
      const match = price.match(/(\d+(?:[\.,]\d+)?)\s*CHF/);
      if (match) {
        const value = match[1].replace(',', '');
        const numValue = parseInt(value);
        return `Ab ${numValue.toLocaleString('de-CH')} CHF`;
      }
    }
    
    // Fallback if no CHF price available
    if (!price || price.includes('anfragen')) {
      return 'Preis auf Anfrage';
    }
    
    return 'Preis auf Anfrage';
  };

  const parsePS = (powerString: string): number => {
    const match = powerString.match(/(\d+)\s*PS/);
    return match ? parseInt(match[1]) : 0;
  };

  const parseNm = (torqueString: string): number => {
    const match = torqueString.match(/(\d+)\s*Nm/);
    return match ? parseInt(match[1]) : 0;
  };

  const getVehicleTypeLabel = (type: string): string => {
    if (!type || typeof type !== 'string') {
      return 'Unbekannter Fahrzeugtyp';
    }
    
    const trimmedType = type.trim();
    
    switch (trimmedType) {
      case 'Kraftfahrzeuge (PKW)': return 'PKW / Autos';
      case 'Motorräder/Quads/Roller': return 'Motorräder & Quads';
      case 'Motorboote': return 'Motorboote';
      case 'Jet Ski\'s': return 'Jet Ski\'s';
      case 'Lastkraftwagen (LKW)': return 'LKW / Nutzfahrzeuge';
      default: 
        // Return the original type if it doesn't match any known types
        return trimmedType || 'Unbekannter Fahrzeugtyp';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Lade Fahrzeugdaten...</span>
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative flex flex-col items-center justify-center min-h-[40vh] sm:min-h-[50vh] bg-cover bg-center bg-no-repeat text-white" style={{
        backgroundImage: 'linear-gradient(rgba(58, 58, 58, 0.8) 0%, rgba(58, 58, 58, 0.9) 100%), url("/DBP_M3_Lucas.jpeg")',
        filter: 'grayscale(100%)'
      }}>
        <div className="text-center p-4 sm:p-8">
          <h1 className="text-3xl font-extrabold leading-tight tracking-tighter sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl">
            Chiptuning Konfigurator
          </h1>
          <p className="mt-4 max-w-2xl mx-auto text-base sm:text-lg text-gray-300 px-4">
            Entwickelt und geprüft auf unserem Allrad-Leistungsprüfstand
          </p>
          <p className="mt-2 max-w-2xl mx-auto text-sm sm:text-base text-gray-400 px-4">
            {totalVehicles} {getVehicleTypeLabel(selectedVehicleType)} verfügbar
          </p>
        </div>
      </section>

      {/* Configurator Section */}
      <section className="py-8 sm:py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

          {/* Vehicle Selection */}
          <div className="mb-8">
            <div className="text-center mb-6">
              <h2 className="text-3xl font-extrabold text-anthracite sm:text-4xl">Fahrzeug konfigurieren</h2>
              <p className="mt-3 text-lg text-gray-600">Wählen Sie Ihr Fahrzeug aus und entdecken Sie das Tuning-Potenzial</p>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4 sm:gap-6">
              {/* Vehicle Type Selection */}
              <div className="bg-gray-50 rounded-lg p-3 sm:p-4 shadow-md">
                <label className="block text-sm font-bold text-anthracite mb-2 sm:mb-3">
                  Fahrzeugtyp
                </label>
                <select
                  value={selectedVehicleType}
                  onChange={(e) => handleVehicleTypeChange(e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent-red focus:border-accent-red transition-colors"
                  disabled={vehicleTypes.length === 0}
                >
            {vehicleTypes.length === 0 ? (
              <option value="">-- Lade Fahrzeugtypen --</option>
            ) : (
              vehicleTypes
                .filter(type => type && type.trim()) // Filter out empty or whitespace-only strings
                .map(type => (
                  <option key={type} value={type}>{getVehicleTypeLabel(type)}</option>
                ))
            )}
          </select>
        </div>

              {/* Brand Selection */}
              <div className="bg-gray-50 rounded-lg p-4 shadow-md">
                <label className="block text-sm font-bold text-anthracite mb-3">
                  Marke wählen
                </label>
                <select
                  value={selectedBrand}
                  onChange={(e) => handleBrandChange(e.target.value)}
                  disabled={brands.length === 0}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent-red focus:border-accent-red transition-colors disabled:bg-gray-100"
                >
            <option value="">-- Marke wählen --</option>
            {brands.map(brand => (
              <option key={brand} value={brand}>{brand}</option>
            ))}
          </select>
        </div>

              {/* Model Selection */}
              <div className="bg-gray-50 rounded-lg p-4 shadow-md">
                <label className="block text-sm font-bold text-anthracite mb-3">
                  Modell wählen
                </label>
                <select
                  value={selectedModel}
                  onChange={(e) => handleModelChange(e.target.value)}
                  disabled={!selectedBrand}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent-red focus:border-accent-red transition-colors disabled:bg-gray-100"
                >
            <option value="">-- Modell wählen --</option>
            {models.map(model => (
              <option key={model} value={model}>{model}</option>
            ))}
          </select>
        </div>

              {/* Year Selection */}
              <div className="bg-gray-50 rounded-lg p-4 shadow-md">
                <label className="block text-sm font-bold text-anthracite mb-3">
                  Baujahr wählen
                </label>
                <select
                  value={selectedYear}
                  onChange={(e) => handleYearChange(e.target.value)}
                  disabled={!selectedModel}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent-red focus:border-accent-red transition-colors disabled:bg-gray-100"
                >
            <option value="">-- Baujahr wählen --</option>
            {years.map(year => (
              <option key={year} value={year}>{year}</option>
            ))}
          </select>
        </div>

              {/* Motor Selection */}
              <div className="bg-gray-50 rounded-lg p-4 shadow-md">
                <label className="block text-sm font-bold text-anthracite mb-3">
                  Motor wählen
                </label>
                <select
                  value={selectedMotor}
                  onChange={(e) => handleMotorChange(e.target.value)}
                  disabled={!selectedYear}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent-red focus:border-accent-red transition-colors disabled:bg-gray-100"
                >
            <option value="">-- Motor wählen --</option>
            {motors.map((motor, index) => (
              <option key={index} value={motor}>{motor}</option>
            ))}
                </select>
              </div>
            </div>
          </div>

          {/* Vehicle Details & Tuning Results */}
          {selectedVehicle && (
            <div className="bg-white rounded-lg shadow-lg p-8 border-l-4 border-accent-red">
              <div className="mb-8">
                <h2 className="text-3xl font-bold text-anthracite mb-4">
                  {selectedVehicle.brand} {selectedVehicle.model}
                </h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 text-sm text-gray-600">
                <div className="bg-gray-50 rounded-lg p-3 sm:p-4">
                  <span className="font-bold text-anthracite">Typ:</span>
                  <p className="text-gray-700">{getVehicleTypeLabel(selectedVehicle.vehicle_type)}</p>
                </div>
                <div className="bg-gray-50 rounded-lg p-3 sm:p-4">
                  <span className="font-bold text-anthracite">Baujahr:</span>
                  <p className="text-gray-700">{selectedVehicle.year}</p>
                </div>
                <div className="bg-gray-50 rounded-lg p-3 sm:p-4">
                  <span className="font-bold text-anthracite">Motor:</span>
                  <p className="text-gray-700">{selectedVehicle.motor}</p>
                </div>
                <div className="bg-gray-50 rounded-lg p-3 sm:p-4">
                  <span className="font-bold text-anthracite">Verfügbare Stages:</span>
                  <p className="text-gray-700">{selectedVehicle.total_stages}</p>
                </div>
            </div>
          </div>

          {/* Tuning Stages */}
          <div className="space-y-6">
            {selectedVehicle.stages.map((stage, index) => {
              const originalPS = parsePS(stage.power_original);
              const tunedPS = parsePS(stage.power_tuned);
              const originalNm = parseNm(stage.torque_original);
              const tunedNm = parseNm(stage.torque_tuned);
              
              // Parse price (currently unused but could be used for calculations)
              // const price = parseFloat(stage.price?.replace(/[^\d.,]/g, '').replace(',', '.') || '0')
              
              return (
                <div key={index} className="bg-white rounded-lg p-6 border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h3 className="text-xl font-bold text-gray-900 mb-1">
                        {stage.name}
                      </h3>
                      {/* Performance Boost Badge */}
                      {originalPS > 0 && tunedPS > 0 && (
                        <div className="inline-flex items-center bg-linear-to-r from-green-400 to-blue-500 text-white text-xs font-bold px-3 py-1 rounded-full">
                          <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M3.293 9.707a1 1 0 010-1.414l6-6a1 1 0 011.414 0l6 6a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L4.707 9.707a1 1 0 01-1.414 0z" clipRule="evenodd" />
                          </svg>
                          +{Math.round(((tunedPS - originalPS) / originalPS) * 100)}% Power
                        </div>
                      )}
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-blue-600">
                        {formatPrice(stage.price)}
                      </div>
                      <div className="text-sm text-gray-500">
                        inkl. MwSt.
                      </div>
                    </div>
                  </div>

                  {/* Overall Performance Gain Summary */}
                  {originalPS > 0 && tunedPS > 0 && originalNm > 0 && tunedNm > 0 && (
                    <div className="bg-linear-to-r from-green-50 via-blue-50 to-purple-50 border-2 border-green-200 rounded-xl p-4 mb-6">
                      <div className="text-center">
                                                 <h4 className="text-lg font-bold text-gray-800 mb-1">Performance-Steigerung</h4>
                         <p className="text-xs text-gray-600 mb-2 font-medium">Mindestangaben - oft höhere Werte möglich</p>
                        <div className="grid grid-cols-2 gap-4">
                          <div className="bg-white/70 rounded-lg p-3 border border-green-200">
                            <div className="text-2xl font-black text-green-600">
                              +{tunedPS - originalPS}
                            </div>
                            <div className="text-sm font-semibold text-green-700">PS mehr</div>
                                                         <div className="text-xs text-green-600">
                               ({Math.round(((tunedPS - originalPS) / originalPS) * 100)}% Steigerung min.)
                             </div>
                          </div>
                          <div className="bg-white/70 rounded-lg p-3 border border-blue-200">
                            <div className="text-2xl font-black text-blue-600">
                              +{tunedNm - originalNm}
                            </div>
                            <div className="text-sm font-semibold text-blue-700">Nm mehr</div>
                                                         <div className="text-xs text-blue-600">
                               ({Math.round(((tunedNm - originalNm) / originalNm) * 100)}% Steigerung min.)
                             </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Power Comparison */}
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h4 className="font-semibold text-gray-900 mb-3">Leistung</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Original:</span>
                          <span className="font-medium">{stage.power_original}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Nach Tuning:</span>
                          <span className="font-medium text-green-600">{stage.power_tuned}</span>
                        </div>
                        <div className="flex justify-between border-t pt-2">
                          <span className="text-gray-600">Differenz:</span>
                          <span className="font-bold text-blue-600">{stage.power_difference}</span>
                        </div>
                      </div>
                      
                      {/* Power Bar Chart - Dramatic Visualization */}
                      {originalPS > 0 && tunedPS > 0 && (
                        <div className="mt-4">
                          <div className="space-y-3">
                            {/* Original Power - Smaller Bar */}
                            <div>
                              <div className="flex items-center justify-between mb-1">
                                <div className="flex items-center space-x-2">
                                  <div className="w-3 h-3 bg-gray-400 rounded"></div>
                                  <span className="text-xs font-medium text-gray-600">Serie</span>
                                </div>
                                <span className="text-xs font-bold text-gray-700">{originalPS} PS</span>
                              </div>
                              <div className="w-full bg-gray-100 rounded-full h-4 shadow-inner">
                                <div 
                                  className="bg-linear-to-r from-gray-300 to-gray-400 h-4 rounded-full shadow-xs transition-all duration-1000 ease-out"
                                  style={{ width: `${Math.min((originalPS / (tunedPS + 50)) * 100, 60)}%` }}
                                ></div>
                              </div>
                            </div>
                            
                            {/* Tuned Power - Dramatic Larger Bar */}
                            <div>
                              <div className="flex items-center justify-between mb-1">
                                <div className="flex items-center space-x-2">
                                  <div className="w-3 h-3 bg-green-500 rounded shadow-xs"></div>
                                  <span className="text-xs font-bold text-green-700">Chiptuning</span>
                                </div>
                                <span className="text-xs font-bold text-green-700">{tunedPS} PS</span>
                              </div>
                              <div className="w-full bg-gray-100 rounded-full h-6 shadow-inner">
                                <div className="bg-linear-to-r from-green-400 via-green-500 to-green-600 h-6 rounded-full shadow-lg transition-all duration-1500 ease-out transform hover:scale-105 animate-pulse"
                                     style={{ width: '100%' }}>
                                  <div className="h-full bg-linear-to-t from-transparent to-white opacity-30 rounded-full"></div>
                                </div>
                              </div>
                            </div>
                            
                            {/* Power Gain Highlight */}
                            <div className="bg-linear-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-2 mt-2">
                              <div className="text-center">
                                <div className="text-lg font-bold text-green-600">
                                  +{tunedPS - originalPS} PS
                                </div>
                                                                 <div className="text-xs text-green-700 font-semibold">
                                   {Math.round(((tunedPS - originalPS) / originalPS) * 100)}% mehr Leistung! (min.)
                                 </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Torque Comparison */}
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h4 className="font-semibold text-gray-900 mb-3">Drehmoment</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Original:</span>
                          <span className="font-medium">{stage.torque_original}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Nach Tuning:</span>
                          <span className="font-medium text-green-600">{stage.torque_tuned}</span>
                        </div>
                        <div className="flex justify-between border-t pt-2">
                          <span className="text-gray-600">Differenz:</span>
                          <span className="font-bold text-blue-600">{stage.torque_difference}</span>
                        </div>
                      </div>

                      {/* Torque Bar Chart - Dramatic Visualization */}
                      {originalNm > 0 && tunedNm > 0 && (
                        <div className="mt-4">
                          <div className="space-y-3">
                            {/* Original Torque - Smaller Bar */}
                            <div>
                              <div className="flex items-center justify-between mb-1">
                                <div className="flex items-center space-x-2">
                                  <div className="w-3 h-3 bg-gray-400 rounded"></div>
                                  <span className="text-xs font-medium text-gray-600">Serie</span>
                                </div>
                                <span className="text-xs font-bold text-gray-700">{originalNm} Nm</span>
                              </div>
                              <div className="w-full bg-gray-100 rounded-full h-4 shadow-inner">
                                <div 
                                  className="bg-linear-to-r from-gray-300 to-gray-400 h-4 rounded-full shadow-xs transition-all duration-1000 ease-out"
                                  style={{ width: `${Math.min((originalNm / (tunedNm + 50)) * 100, 60)}%` }}
                                ></div>
                              </div>
                            </div>
                            
                            {/* Tuned Torque - Dramatic Larger Bar */}
                            <div>
                              <div className="flex items-center justify-between mb-1">
                                <div className="flex items-center space-x-2">
                                  <div className="w-3 h-3 bg-blue-500 rounded shadow-xs"></div>
                                  <span className="text-xs font-bold text-blue-700">Chiptuning</span>
                                </div>
                                <span className="text-xs font-bold text-blue-700">{tunedNm} Nm</span>
                              </div>
                              <div className="w-full bg-gray-100 rounded-full h-6 shadow-inner">
                                <div className="bg-linear-to-r from-blue-400 via-blue-500 to-blue-600 h-6 rounded-full shadow-lg transition-all duration-1500 ease-out transform hover:scale-105 animate-pulse"
                                     style={{ width: '100%' }}>
                                  <div className="h-full bg-linear-to-t from-transparent to-white opacity-30 rounded-full"></div>
                                </div>
                              </div>
                            </div>
                            
                            {/* Torque Gain Highlight */}
                            <div className="bg-linear-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-2 mt-2">
                              <div className="text-center">
                                <div className="text-lg font-bold text-blue-600">
                                  +{tunedNm - originalNm} Nm
                                </div>
                                                                 <div className="text-xs text-blue-700 font-semibold">
                                   {Math.round(((tunedNm - originalNm) / originalNm) * 100)}% mehr Drehmoment! (min.)
                                 </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex flex-col sm:flex-row gap-3 mt-6">
                    <button className="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors">
                      {stage.name} anfragen
                    </button>
                    <button className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-800 font-medium py-3 px-6 rounded-lg transition-colors">
                      Mehr Details
                    </button>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* No Data Message */}
      {!loading && totalVehicles === 0 && (
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <div className="text-gray-500 mb-2">
            Keine Fahrzeuge für {getVehicleTypeLabel(selectedVehicleType)} verfügbar
          </div>
          <div className="text-sm text-gray-400">
            Versuche einen anderen Fahrzeugtyp
          </div>
        </div>
      )}

          {/* Footer Info */}
          <div className="mt-8 text-center text-sm text-gray-500">
            <p>Alle Leistungsangaben wurden auf unserem Allrad-Leistungsprüfstand gemessen</p>
            <p className="mt-1 font-semibold text-gray-600">Angaben sind Mindestleistungen - oft sind höhere Werte möglich</p>
            <p className="mt-1">Preise verstehen sich zzgl. MwSt. und Einbau</p>
            <p className="mt-1 text-xs">
              Daten extrahiert am: {selectedVehicle?.scraped_at ? new Date(selectedVehicle.scraped_at).toLocaleDateString('de-DE') : 'Aktuell'}
            </p>
          </div>
        </div>
      </section>
    </div>
  );
};

export default TuningConfigurator; 