'use client'

import { useState } from 'react'
import Link from 'next/link'
import ServiceTextSwiper from './ServiceTextSwiper'

interface SwipingService {
  text: string
  order: number
}

interface HeroSectionProps {
  heroTitle: string
  heroSubtitle: string
  heroDescription: string
  swipingServices?: SwipingService[]
}

export default function HeroSection({ 
  heroTitle, 
  heroSubtitle, 
  heroDescription, 
  swipingServices 
}: HeroSectionProps) {
  const [currentServiceIndex, setCurrentServiceIndex] = useState(0)

  // Service configuration - alle weiß mit passenden Texten
  const serviceConfig = [
    {
      text: 'Tuning Konfigurator',
      href: '/tuning',
      className: 'bg-white text-anthracite hover:bg-gray-100',
      subtitle: 'Maximieren Sie die Leistung Ihres Fahrzeugs mit professionellem Chiptuning',
      description: 'Bis zu 30% mehr Leistung und besserer Kraftstoffverbrauch durch Software-Optimierung.'
    },
    {
      text: 'Werkstatt-Services',
      href: '/services',
      className: 'bg-white text-anthracite hover:bg-gray-100',
      subtitle: 'Komplette Werkstattdienstleistungen für Ihr Fahrzeug',
      description: 'Von Ölservice bis Getriebewartung - alles aus einer Hand mit modernster Technik.'
    },
    {
      text: 'Fahrzeugaufbereitung',
      href: '/fahrzeugaufbereitung',
      className: 'bg-white text-anthracite hover:bg-gray-100',
      subtitle: 'Professionelle Fahrzeugpflege für perfekten Glanz und Werterhalt',
      description: 'Innen- und Außenreinigung, Lackaufbereitung und Schutzversiegelungen vom Profi.'
    }
  ]

  const currentConfig = serviceConfig[currentServiceIndex]

  const handlePrevious = () => {
    const newIndex = currentServiceIndex === 0
      ? (swipingServices?.length || 1) - 1
      : currentServiceIndex - 1
    setCurrentServiceIndex(newIndex)
  }

  const handleNext = () => {
    const newIndex = (currentServiceIndex + 1) % (swipingServices?.length || 1)
    setCurrentServiceIndex(newIndex)
  }

  const handleDotClick = (index: number) => {
    setCurrentServiceIndex(index)
  }

  return (
    <section className="relative flex flex-col items-center justify-center bg-cover bg-center bg-no-repeat text-white" style={{
      backgroundImage: 'linear-gradient(rgba(58, 58, 58, 0.8) 0%, rgba(58, 58, 58, 0.9) 100%), url("/DBP_M3_Lucas.jpeg")',
      filter: 'grayscale(100%)',
      height: '55vh',
      minHeight: '450px'
    }}>
      {/* Navigation Arrows */}
      {swipingServices && swipingServices.length > 1 && (
        <>
          <button
            onClick={handlePrevious}
            className="absolute left-4 top-1/2 transform -translate-y-1/2 p-2 transition-all duration-300 z-10 cursor-pointer hover:opacity-100 hover:scale-110"
            aria-label="Vorheriger Service"
          >
            <svg className="w-8 h-8 text-gray-400 opacity-50 transition-all duration-300" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          </button>
          <button
            onClick={handleNext}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 p-2 transition-all duration-300 z-10 cursor-pointer hover:opacity-100 hover:scale-110"
            aria-label="Nächster Service"
          >
            <svg className="w-8 h-8 text-gray-400 opacity-50 transition-all duration-300" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
            </svg>
          </button>
        </>
      )}

      <div className="text-center p-4 sm:p-8 w-full max-w-6xl mx-auto">
        <div style={{ minHeight: '4em' }} className="flex items-center justify-center">
          {swipingServices && swipingServices.length > 0 ? (
            <h1 className="text-2xl font-extrabold leading-tight tracking-tighter sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl text-white text-center">
              <ServiceTextSwiper
                services={swipingServices}
                interval={3500}
                className="text-white"
                onServiceChange={setCurrentServiceIndex}
                currentIndex={currentServiceIndex}
              />
            </h1>
          ) : (
            <h1 className="text-2xl font-extrabold leading-tight tracking-tighter sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl text-white text-center">
              {heroTitle || "Professionelle Fahrzeugoptimierung auf höchstem Niveau"}
            </h1>
          )}
        </div>
        <div style={{ minHeight: '5em' }} className="flex flex-col justify-center mt-2">
          <p className="max-w-2xl mx-auto text-base sm:text-lg text-gray-300 px-4 leading-snug">
            {swipingServices && swipingServices.length > 0 ? currentConfig?.subtitle : heroSubtitle}
          </p>
          <p className="mt-2 max-w-2xl mx-auto text-sm sm:text-base text-gray-400 px-4 leading-snug">
            {swipingServices && swipingServices.length > 0 ? currentConfig?.description : heroDescription}
          </p>
        </div>

        {/* Dot Navigation */}
        {swipingServices && swipingServices.length > 1 && (
          <div className="mt-4 flex justify-center space-x-3">
            {swipingServices.map((_, index) => (
              <button
                key={index}
                onClick={() => handleDotClick(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 cursor-pointer hover:scale-125 ${
                  index === currentServiceIndex
                    ? 'bg-white shadow-lg'
                    : 'bg-white bg-opacity-40 hover:bg-opacity-80'
                }`}
                aria-label={`Service ${index + 1} auswählen`}
              />
            ))}
          </div>
        )}

        {/* Dynamic Service Button */}
        {swipingServices && swipingServices.length > 0 && currentConfig && (
          <div className="mt-5 flex justify-center">
            <Link
              href={currentConfig.href}
              className={`px-6 py-3 rounded-lg font-semibold transition-colors duration-300 shadow-lg ${currentConfig.className}`}
            >
              {currentConfig.text}
            </Link>
          </div>
        )}
      </div>
    </section>
  )
}
