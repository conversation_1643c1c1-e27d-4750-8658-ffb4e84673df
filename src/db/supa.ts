import { createClient } from '@supabase/supabase-js'

// Environment variables validation
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

// Create singleton Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: false, // No auth needed for this MVP
  },
})

// Define the Database type based on the public schema
export type Database = {
  public: {
    Tables: {
      vehicles: {
        Row: Vehicle
        Insert: Omit<Vehicle, 'id' | 'created_at'>
        Update: Partial<Omit<Vehicle, 'id' | 'created_at'>>
      }
      stages: {
        Row: Stage
        Insert: Omit<Stage, 'id' | 'created_at'>
        Update: Partial<Omit<Stage, 'id' | 'created_at'>>
      }
    }
  }
}

// Database types (updated to match imported data schema)
export interface Vehicle {
  id: string
  vehicle_type: string
  brand: string
  model: string
  year: string | null
  engine: string
  created_at: string
}

export interface Stage {
  id: string
  vehicle_id: string
  name: string
  original_power: number
  tuned_power: number
  price_chf: number
  description: string
  created_at: string
}

// Database helper functions
export const db = {
  vehicles: {
    async getAll(filters?: {
      type?: string
      brand?: string
      year?: string
      engine?: string
      search?: string
    }) {
      let query = supabase.from('vehicles').select('*')

      if (filters?.type) {
        query = query.eq('vehicle_type', filters.type)
      }
      if (filters?.brand) {
        query = query.eq('brand', filters.brand)
      }
      if (filters?.year) {
        query = query.eq('year', filters.year)
      }
      if (filters?.engine) {
        query = query.eq('engine', filters.engine)
      }
      if (filters?.search) {
        query = query.or(
          `brand.ilike.%${filters.search}%,model.ilike.%${filters.search}%,engine.ilike.%${filters.search}%`
        )
      }

      const { data, error } = await query
        .order('brand', { ascending: true })
        .limit(50) // Limit for performance

      if (error) {
        console.error('Error fetching vehicles:', error)
        throw new Error('Failed to fetch vehicles')
      }

      return data as Vehicle[]
    },

    async getById(id: string) {
      const { data, error } = await supabase
        .from('vehicles')
        .select('*')
        .eq('id', id)
        .single()

      if (error) {
        console.error('Error fetching vehicle:', error)
        throw new Error('Failed to fetch vehicle')
      }

      return data as Vehicle
    },

    async getFilterOptions(filters?: { type?: string; brand?: string }) {
      try {
        // 1) Alle Fahrzeugtypen werden immer benötigt
        const typesPromise = supabase.rpc('get_distinct_vehicle_types', {})

        // 2) Marken – jetzt über parametrische RPC
        const brandsPromise = supabase.rpc('get_distinct_brands', {
          p_vehicle_type: filters?.type || null,
        })

        // 3) Jahre – parametrische RPC (Typ + Marke möglich)
        const yearsPromise = supabase.rpc('get_distinct_years', {
          p_vehicle_type: filters?.type || null,
          p_brand: filters?.brand || null,
        })

        const [typesResult, brandsResult, yearsResult] = await Promise.all([
          typesPromise,
          brandsPromise,
          yearsPromise,
        ])

        if (typesResult.error || brandsResult.error || yearsResult.error) {
          console.error('Filter option error', {
            typesError: typesResult.error,
            brandsError: brandsResult.error,
            yearsError: yearsResult.error,
          })
          throw new Error('Failed to fetch filter options')
        }

        return {
          types: (typesResult.data || []).map((row: { vehicle_type: string }) => row.vehicle_type).sort(),
          brands: Array.from(new Set((brandsResult.data || []).map((row: { brand: string }) => row.brand))).sort(),
          years: Array.from(new Set((yearsResult.data || []).map((row: { year: string }) => row.year).filter(Boolean))).sort(),
        }
      } catch (error) {
        console.error('Error fetching filter options:', error)
        throw new Error('Failed to fetch filter options')
      }
    },
  },

  stages: {
    async getByVehicleId(vehicleId: string) {
      const { data, error } = await supabase
        .from('stages')
        .select('*')
        .eq('vehicle_id', vehicleId)
        .order('name', { ascending: true })

      if (error) {
        console.error('Error fetching stages:', error)
        throw new Error('Failed to fetch stages')
      }

      return data as Stage[]
    },
  },
} 