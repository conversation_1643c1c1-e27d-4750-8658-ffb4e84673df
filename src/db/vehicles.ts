import { SupabaseClient } from '@supabase/supabase-js'
import { Database } from './supa'
import { Vehicle } from '@/sanity/types'

// Extract the stage type from the Vehicle type
type VehicleStage = NonNullable<Vehicle['stages']>[number]

/**
 * Upserts a vehicle and its stages into the Supabase database.
 * This function will create or update a vehicle based on its Sanity ID.
 * It will also delete and re-insert all associated stages to ensure consistency.
 */
export async function upsertVehicleFromSanity(
  supabase: SupabaseClient<Database>,
  vehicle: Vehicle, // This should be the type for a vehicle from Sanity
) {
  if (!vehicle._id) {
    throw new Error('Sanity vehicle data must have an _id.')
  }

  // 1. Upsert the vehicle
  const { data: vehicleData, error: vehicleError } = await supabase
    .from('vehicles')
    .upsert(
      {
        sanity_id: vehicle._id,
        vehicle_type: vehicle.vehicleType,
        brand: vehicle.brand,
        model: vehicle.model,
        year: vehicle.year,
        engine: vehicle.engine,
      },
      { onConflict: 'sanity_id' },
    )
    .select('id')
    .single()

  if (vehicleError) {
    console.error('Error upserting vehicle:', vehicleError)
    throw new Error(`Failed to upsert vehicle: ${vehicleError.message}`)
  }

  const dbVehicleId = vehicleData.id

  // 2. Delete all existing stages for this vehicle to ensure a clean slate
  const { error: deleteStagesError } = await supabase
    .from('stages')
    .delete()
    .match({ vehicle_id: dbVehicleId })

  if (deleteStagesError) {
    console.error('Error deleting old stages:', deleteStagesError)
    throw new Error(`Failed to delete old stages: ${deleteStagesError.message}`)
  }

  // 3. Insert the new stages, if any
  if (vehicle.stages && vehicle.stages.length > 0) {
    const newStages = vehicle.stages.map((stage: VehicleStage) => ({
      vehicle_id: dbVehicleId,
      name: stage.name,
      original_power: vehicle.originalPower, // Use vehicle's original power
      tuned_power: stage.tunedPower,
      price_chf: stage.price,
      description: stage.description,
    }))

    const { error: insertStagesError } = await supabase
      .from('stages')
      .insert(newStages)

    if (insertStagesError) {
      console.error('Error inserting new stages:', insertStagesError)
      throw new Error(
        `Failed to insert new stages: ${insertStagesError.message}`,
      )
    }
  }

  return { vehicleId: dbVehicleId }
}

/**
 * Deletes a vehicle and its associated stages from the Supabase database
 * based on the Sanity document ID.
 */
export async function deleteVehicleBySanityId(
  supabase: SupabaseClient<Database>,
  sanityId: string,
) {
  if (!sanityId) {
    throw new Error('Sanity ID is required for deletion.')
  }

  const { error } = await supabase
    .from('vehicles')
    .delete()
    .match({ sanity_id: sanityId })

  if (error) {
    console.error('Error deleting vehicle:', error)
    throw new Error(`Failed to delete vehicle: ${error.message}`)
  }

  return { success: true }
} 