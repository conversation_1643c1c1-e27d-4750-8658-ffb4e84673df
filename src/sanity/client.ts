import { createClient } from '@sanity/client'

// Environment variables validation
const projectId = process.env.NEXT_PUBLIC_SANITY_PROJECT_ID
const dataset = process.env.NEXT_PUBLIC_SANITY_DATASET || 'production'
const apiVersion = process.env.NEXT_PUBLIC_SANITY_API_VERSION || '2024-01-01'
const token = process.env.SANITY_API_READ_TOKEN

if (!projectId) {
  throw new Error('Missing NEXT_PUBLIC_SANITY_PROJECT_ID environment variable')
}

// Create Sanity client
export const sanityClient = createClient({
  projectId,
  dataset,
  apiVersion,
  token,
  useCdn: true, // Use CDN for faster read operations
})

// Client for server-side operations (with token)
export const sanityServerClient = createClient({
  projectId,
  dataset,
  apiVersion,
  token,
  useCdn: false, // Don't use CDN for mutations
})

// Preview client for draft content
export const sanityPreviewClient = createClient({
  projectId,
  dataset,
  apiVersion,
  token,
  useCdn: false,
  perspective: 'previewDrafts',
}) 