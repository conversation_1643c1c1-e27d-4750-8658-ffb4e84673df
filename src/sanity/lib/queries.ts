import { groq } from 'next-sanity'

// Query for services page content
export const servicesPageQuery = groq`
  *[_type == "servicesPage"][0]{
    heroTitle,
    heroSubtitle,
    heroDescription,
    services[] | order(order asc) {
      title,
      description,
      features,
      buttonText,
      buttonLink,
      isExternalLink,
      color,
      icon,
      order
    },
    ctaTitle,
    ctaDescription,
    ctaButtonText
  }
`

// Query for homepage content
export const homepageQuery = groq`
  *[_type == "homepage"][0]{
    heroTitle,
    heroSubtitle,
    heroDescription,
    ctaButtonText,
    ctaSecondaryText,
    features[] | order(order asc) {
      title,
      description,
      icon,
      order
    },
    swipingServices[] | order(order asc) {
      text,
      order
    },
    detailedServices[] | order(order asc) {
      title,
      order,
      services[] {
        title,
        description
      }
    }
  }
`

// Query for tuning page content
export const tuningPageQuery = groq`
  *[_type == "tuningPage"][0]{
    pageTitle,
    pageSubtitle,
    configuratorSettings {
      loadingText,
      introText,
      defaultVehicleType,
      pricePrefix,
      priceSuffix,
      priceIncludesTax,
      minSpecsDisclaimer
    },
    labels {
      vehicleTypeLabel,
      brandLabel,
      modelLabel,
      yearLabel,
      motorLabel,
      selectPlaceholder,
      performanceTitle,
      originalLabel,
      tunedLabel,
      differenceLabel
    }
  }
`

// Query for site settings
export const siteSettingsQuery = groq`
  *[_type == "siteSettings"][0]{
    siteName,
    siteDescription,
    logo,
    navigation {
      homeLabel,
      servicesLabel,
      tuningLabel,
      detailingLabel,
      contactLabel
    },
    footer {
      companyName,
      address,
      rightsText,
      legalSectionTitle,
      contactSectionTitle,
      phone,
      email,
      whatsapp,
      legalLinks {
        imprintText,
        privacyText,
        termsText
      }
    }
  }
`

// Detailing Page Query
export const detailingPageQuery = groq`*[_type == "detailingPage"][0] {
  _id,
  title,
  heroSection {
    heroTitle,
    heroSubtitle,
    heroDescription
  },
  services[] {
    title,
    description,
    features,
    buttonText,
    buttonLink,
    isExternalLink,
    color,
    icon,
    order,
    _key
  } | order(order asc),
  ctaSection {
    ctaTitle,
    ctaDescription,
    ctaButtonText
  },
  lastUpdated
}` 