/* eslint-disable @typescript-eslint/no-explicit-any */
import { type SchemaTypeDefinition } from 'sanity'



// Service schema
const service: SchemaTypeDefinition = {
  name: 'service',
  title: 'Service',
  type: 'document',
  fields: [
    {
      name: 'title',
      title: 'Title',
      type: 'string',
      validation: Rule => Rule.required(),
    },
    {
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'title',
        maxLength: 96,
      },
      validation: Rule => Rule.required(),
    },
    {
      name: 'description',
      title: 'Description',
      type: 'text',
      rows: 4,
    },
    {
      name: 'content',
      title: 'Content',
      type: 'array',
      of: [
        {
          type: 'block',
        },
        {
          type: 'image',
          options: {
            hotspot: true,
          },
          fields: [
            {
              name: 'alt',
              title: 'Alt Text',
              type: 'string',
            },
          ],
        },
      ],
    },
    {
      name: 'image',
      title: 'Featured Image',
      type: 'image',
      options: {
        hotspot: true,
      },
      fields: [
        {
          name: 'alt',
          title: 'Alt Text',
          type: 'string',
        },
      ],
    },
    {
      name: 'category',
      title: 'Category',
      type: 'string',
      options: {
        list: [
          { title: 'Reparatur', value: 'repair' },
          { title: 'Aufbereitung', value: 'detailing' },
          { title: 'Software-Tuning', value: 'tuning' },
        ],
      },
      validation: Rule => Rule.required(),
    },
    {
      name: 'order',
      title: 'Display Order',
      type: 'number',
      initialValue: 0,
    },
  ],
  orderings: [
    {
      title: 'Display Order',
      name: 'orderAsc',
      by: [{ field: 'order', direction: 'asc' }],
    },
  ],
}

// Legal page schema
const legalPage: SchemaTypeDefinition = {
  name: 'legalPage',
  title: 'Legal Page',
  type: 'document',
  fields: [
    {
      name: 'title',
      title: 'Title',
      type: 'string',
      validation: Rule => Rule.required(),
    },
    {
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'title',
        maxLength: 96,
      },
      validation: Rule => Rule.required(),
    },
    {
      name: 'content',
      title: 'Content',
      type: 'array',
      of: [
        {
          type: 'block',
        },
      ],
      validation: Rule => Rule.required(),
    },
    {
      name: 'lastUpdated',
      title: 'Last Updated',
      type: 'datetime',
      initialValue: () => new Date().toISOString(),
    },
  ],
}

// Services page schema
const servicesPage: SchemaTypeDefinition = {
  name: 'servicesPage',
  title: 'Services Page',
  type: 'document',
  fields: [
    {
      name: 'heroTitle',
      title: 'Hero Title',
      type: 'string',
      validation: Rule => Rule.required(),
    },
    {
      name: 'heroSubtitle',
      title: 'Hero Subtitle',
      type: 'string',
    },
    {
      name: 'heroDescription',
      title: 'Hero Description',
      type: 'text',
      rows: 3,
    },
    {
      name: 'services',
      title: 'Services',
      type: 'array',
      of: [
        {
          name: 'serviceCard',
          title: 'Service Card',
          type: 'object',
          fields: [
            {
              name: 'title',
              title: 'Title',
              type: 'string',
              validation: Rule => Rule.required(),
            },
            {
              name: 'description',
              title: 'Description',
              type: 'text',
              rows: 3,
            },
            {
              name: 'features',
              title: 'Features',
              type: 'array',
              of: [{ type: 'string' }],
            },
            {
              name: 'buttonText',
              title: 'Button Text',
              type: 'string',
            },
            {
              name: 'buttonLink',
              title: 'Button Link',
              type: 'string',
            },
            {
              name: 'isExternalLink',
              title: 'Is External Link',
              type: 'boolean',
              initialValue: false,
            },
            {
              name: 'color',
              title: 'Color',
              type: 'string',
              options: {
                list: [
                  { title: 'Blue', value: 'blue' },
                  { title: 'Green', value: 'green' },
                  { title: 'Orange', value: 'orange' },
                  { title: 'Purple', value: 'purple' },
                  { title: 'Red', value: 'red' },
                  { title: 'Teal', value: 'teal' },
                ],
              },
            },
            {
              name: 'icon',
              title: 'Icon',
              type: 'string',
              options: {
                list: [
                  { title: 'Lightning', value: 'lightning' },
                  { title: 'Chart', value: 'chart' },
                  { title: 'Cog', value: 'cog' },
                  { title: 'Clock', value: 'clock' },
                  { title: 'Beaker', value: 'beaker' },
                  { title: 'Chat', value: 'chat' },
                ],
              },
            },
            {
              name: 'order',
              title: 'Display Order',
              type: 'number',
              initialValue: 1,
            },
          ],
          preview: {
            select: {
              title: 'title',
              subtitle: 'description',
            },
          },
        },
      ],
    },
    {
      name: 'ctaTitle',
      title: 'CTA Title',
      type: 'string',
    },
    {
      name: 'ctaDescription',
      title: 'CTA Description',
      type: 'text',
      rows: 2,
    },
    {
      name: 'ctaButtonText',
      title: 'CTA Button Text',
      type: 'string',
    },
  ],
}

// Homepage schema
const homepage: SchemaTypeDefinition = {
  name: 'homepage',
  title: 'Homepage',
  type: 'document',
  fields: [
    {
      name: 'heroTitle',
      title: 'Hero Title',
      type: 'string',
      validation: Rule => Rule.required(),
    },
    {
      name: 'heroSubtitle',
      title: 'Hero Subtitle',
      type: 'text',
      rows: 2,
    },
    {
      name: 'heroDescription',
      title: 'Hero Description',
      type: 'text',
      rows: 3,
    },
    {
      name: 'ctaButtonText',
      title: 'Primary CTA Button Text',
      type: 'string',
    },
    {
      name: 'ctaSecondaryText',
      title: 'Secondary CTA Button Text',
      type: 'string',
    },
    {
      name: 'features',
      title: 'Feature Cards',
      type: 'array',
      of: [
        {
          name: 'featureCard',
          title: 'Feature Card',
          type: 'object',
          fields: [
            {
              name: 'title',
              title: 'Title',
              type: 'string',
              validation: Rule => Rule.required(),
            },
            {
              name: 'description',
              title: 'Description',
              type: 'text',
              rows: 3,
            },
            {
              name: 'icon',
              title: 'Icon',
              type: 'string',
              options: {
                list: [
                  { title: 'Lightning (Performance)', value: 'lightning' },
                  { title: 'Check (Quality)', value: 'check' },
                  { title: 'Clock (Speed)', value: 'clock' },
                  { title: 'Chart (Analytics)', value: 'chart' },
                  { title: 'Cog (Technical)', value: 'cog' },
                  { title: 'Shield (Security)', value: 'shield' },
                ],
              },
            },
            {
              name: 'order',
              title: 'Display Order',
              type: 'number',
              initialValue: 1,
            },
          ],
          preview: {
            select: {
              title: 'title',
              subtitle: 'description',
            },
          },
        },
      ],
    },
  ],
}

// Tuning page schema
const tuningPage: SchemaTypeDefinition = {
  name: 'tuningPage',
  title: 'Tuning Page',
  type: 'document',
  fields: [
    {
      name: 'pageTitle',
      title: 'Page Title',
      type: 'string',
      validation: Rule => Rule.required(),
    },
    {
      name: 'pageSubtitle',
      title: 'Page Subtitle',
      type: 'text',
      rows: 2,
    },
    {
      name: 'configuratorSettings',
      title: 'Configurator Settings',
      type: 'object',
      fields: [
        {
          name: 'loadingText',
          title: 'Loading Text',
          type: 'string',
          initialValue: 'Lade Konfigurator...',
        },
        {
          name: 'introText',
          title: 'Introduction Text',
          type: 'text',
          rows: 3,
        },
        {
          name: 'defaultVehicleType',
          title: 'Default Vehicle Type',
          type: 'string',
          initialValue: 'Kraftfahrzeuge (PKW)',
        },
        {
          name: 'pricePrefix',
          title: 'Price Prefix (e.g., "Ab")',
          type: 'string',
          initialValue: 'Ab',
        },
        {
          name: 'priceSuffix',
          title: 'Price Suffix (e.g., "CHF")',
          type: 'string',
          initialValue: 'CHF',
        },
        {
          name: 'priceIncludesTax',
          title: 'Price Includes Tax Text',
          type: 'string',
          initialValue: 'inkl. MwSt.',
        },
        {
          name: 'minSpecsDisclaimer',
          title: 'Minimum Specifications Disclaimer',
          type: 'string',
          initialValue: 'Mindestangaben',
        },
      ],
    },
    {
      name: 'labels',
      title: 'UI Labels',
      type: 'object',
      fields: [
        {
          name: 'vehicleTypeLabel',
          title: 'Vehicle Type Label',
          type: 'string',
          initialValue: 'Fahrzeugtyp',
        },
        {
          name: 'brandLabel',
          title: 'Brand Label',
          type: 'string',
          initialValue: 'Marke',
        },
        {
          name: 'modelLabel',
          title: 'Model Label',
          type: 'string',
          initialValue: 'Modell',
        },
        {
          name: 'yearLabel',
          title: 'Year Label',
          type: 'string',
          initialValue: 'Baujahr',
        },
        {
          name: 'motorLabel',
          title: 'Motor Label',
          type: 'string',
          initialValue: 'Motor',
        },
        {
          name: 'selectPlaceholder',
          title: 'Select Placeholder',
          type: 'string',
          initialValue: 'Wählen Sie...',
        },
        {
          name: 'performanceTitle',
          title: 'Performance Section Title',
          type: 'string',
          initialValue: 'Leistungssteigerung',
        },
        {
          name: 'originalLabel',
          title: 'Original Value Label',
          type: 'string',
          initialValue: 'Original',
        },
        {
          name: 'tunedLabel',
          title: 'Tuned Value Label',
          type: 'string',
          initialValue: 'Getunt',
        },
        {
          name: 'differenceLabel',
          title: 'Difference Label',
          type: 'string',
          initialValue: 'Unterschied',
        },
      ],
    },
  ],
}

// Updated site settings schema
const updatedSiteSettings: SchemaTypeDefinition = {
  name: 'siteSettings',
  title: 'Site Settings',
  type: 'document',
  fields: [
    {
      name: 'siteName',
      title: 'Site Name',
      type: 'string',
      validation: Rule => Rule.required(),
      initialValue: 'DB-Performance',
    },
    {
      name: 'siteDescription',
      title: 'Site Description',
      type: 'text',
      rows: 3,
    },
    {
      name: 'logo',
      title: 'Logo',
      type: 'image',
      options: {
        hotspot: true,
      },
    },
    {
      name: 'navigation',
      title: 'Navigation',
      type: 'object',
      fields: [
        {
          name: 'homeLabel',
          title: 'Home Label',
          type: 'string',
          initialValue: 'Home',
        },
        {
          name: 'servicesLabel',
          title: 'Services Label',
          type: 'string',
          initialValue: 'Services',
        },
        {
          name: 'tuningLabel',
          title: 'Tuning Label',
          type: 'string',
          initialValue: 'Tuning-Konfigurator',
        },
        {
          name: 'contactLabel',
          title: 'Contact Label',
          type: 'string',
          initialValue: 'Kontakt',
        },
      ],
    },
    {
      name: 'footer',
      title: 'Footer',
      type: 'object',
      fields: [
        {
          name: 'companyName',
          title: 'Company Name',
          type: 'string',
          validation: Rule => Rule.required(),
          initialValue: 'DB-Performance Garage Bytyci',
        },
        {
          name: 'address',
          title: 'Address',
          type: 'text',
          rows: 3,
          initialValue: 'Stauseestrasse 1, 5316 Leuggern, Schweiz',
        },
        {
          name: 'rightsText',
          title: 'Rights Text',
          type: 'string',
          initialValue: 'Alle Rechte vorbehalten',
        },
        {
          name: 'legalSectionTitle',
          title: 'Legal Section Title',
          type: 'string',
          initialValue: 'Rechtliches',
        },
        {
          name: 'contactSectionTitle',
          title: 'Contact Section Title',
          type: 'string',
          initialValue: 'Kontakt',
        },
        {
          name: 'phone',
          title: 'Phone Number',
          type: 'string',
          initialValue: '+41 XX XXX XX XX',
        },
        {
          name: 'email',
          title: 'Email Address',
          type: 'string',
          validation: Rule => Rule.email(),
          initialValue: '<EMAIL>',
        },
        {
          name: 'legalLinks',
          title: 'Legal Links',
          type: 'object',
          fields: [
            {
              name: 'imprintText',
              title: 'Impressum Link Text',
              type: 'string',
              initialValue: 'Impressum',
            },
            {
              name: 'privacyText',
              title: 'Privacy Policy Link Text',
              type: 'string',
              initialValue: 'Datenschutz',
            },
            {
              name: 'termsText',
              title: 'Terms Link Text',
              type: 'string',
              initialValue: 'AGB',
            },
          ],
        },
      ],
    },
    {
      name: 'swipingServices',
      title: 'Automatisch wechselnde Service-Texte',
      type: 'array',
      description: 'Texte die automatisch in der Hero-Section wechseln',
      of: [
        {
          name: 'swipingService',
          title: 'Service Text',
          type: 'object',
          fields: [
            {
              name: 'text',
              title: 'Service Text',
              type: 'string',
              validation: Rule => Rule.required(),
              description: 'z.B. "Tuning", "Werkstatt-Services", "Fahrzeugaufbereitung"',
            },
            {
              name: 'order',
              title: 'Reihenfolge',
              type: 'number',
              initialValue: 1,
            },
          ],
          preview: {
            select: {
              title: 'text',
              order: 'order',
            },
            prepare({ title, order }: any) {
              return {
                title: title || 'Unbenannter Service',
                subtitle: `Reihenfolge: ${order || 1}`,
              }
            },
          },
        },
      ],
    },
    {
      name: 'detailedServices',
      title: 'Detaillierte Dienstleistungen',
      type: 'array',
      of: [
        {
          name: 'serviceSection',
          title: 'Service Section',
          type: 'object',
          fields: [
            {
              name: 'title',
              title: 'Section Title',
              type: 'string',
              validation: Rule => Rule.required(),
            },
            {
              name: 'services',
              title: 'Services',
              type: 'array',
              of: [
                {
                  name: 'serviceItem',
                  title: 'Service Item',
                  type: 'object',
                  fields: [
                    {
                      name: 'title',
                      title: 'Service Title',
                      type: 'string',
                      validation: Rule => Rule.required(),
                    },
                    {
                      name: 'description',
                      title: 'Service Description',
                      type: 'text',
                      rows: 3,
                      validation: Rule => Rule.required(),
                    },
                  ],
                },
              ],
            },
            {
              name: 'order',
              title: 'Display Order',
              type: 'number',
              initialValue: 1,
            },
          ],
        },
      ],
    },
  ],
}

// Vehicle schema
const vehicle: SchemaTypeDefinition = {
  name: 'vehicle',
  title: 'Fahrzeug',
  type: 'document',
  icon: () => '🚗',
  fields: [
    {
      name: 'brand',
      title: 'Marke',
      type: 'string',
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'model',
      title: 'Modell',
      type: 'string',
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'year',
      title: 'Baujahr',
      type: 'string',
      description: 'z.B. 2020 oder 2018-2022',
    },
    {
      name: 'engine',
      title: 'Motor',
      type: 'string',
      validation: (Rule: any) => Rule.required(),
      description: 'z.B. 2.0 TDI, 3.0 TFSI, etc.',
    },
    {
      name: 'vehicleType',
      title: 'Fahrzeugtyp',
      type: 'string',
      options: {
        list: [
          { title: 'Auto', value: 'car' },
          { title: 'Motorrad', value: 'motorcycle' },
          { title: 'LKW', value: 'truck' },
          { title: 'Transporter', value: 'van' },
        ],
      },
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'originalPower',
      title: 'Originalleistung (PS)',
      type: 'number',
      description: 'Serienleistung in PS',
    },
    {
      name: 'originalTorque',
      title: 'Originaldrehmoment (Nm)',
      type: 'number',
      description: 'Seriendrehmoment in Nm',
    },
    {
      name: 'stages',
      title: 'Tuning-Stages',
      type: 'array',
      of: [
        {
          type: 'object',
          name: 'stage',
          title: 'Stage',
          fields: [
            {
              name: 'name',
              title: 'Stage Name',
              type: 'string',
              validation: (Rule: any) => Rule.required(),
              description: 'z.B. Stage 1, Stage 2, Stage 2+',
            },
            {
              name: 'tunedPower',
              title: 'Geleistete Leistung (PS)',
              type: 'number',
              validation: (Rule: any) => Rule.required(),
            },
            {
              name: 'tunedTorque',
              title: 'Geleistetes Drehmoment (Nm)',
              type: 'number',
            },
            {
              name: 'price',
              title: 'Preis (CHF)',
              type: 'number',
              validation: (Rule: any) => Rule.required(),
            },
            {
              name: 'description',
              title: 'Beschreibung',
              type: 'text',
              rows: 3,
              description: 'Kurze Beschreibung der Stage-Modifikationen',
            },
            {
              name: 'features',
              title: 'Features',
              type: 'array',
              of: [{ type: 'string' }],
              description: 'Liste der Verbesserungen',
            },
          ],
          preview: {
            select: {
              title: 'name',
              subtitle: 'tunedPower',
              price: 'price',
            },
            prepare({ title, subtitle, price }: any) {
              return {
                title: title || 'Unbenannte Stage',
                subtitle: `${subtitle || 'N/A'} PS - CHF ${price || 'N/A'}`,
              }
            },
          },
        },
      ],
    },
    {
      name: 'image',
      title: 'Fahrzeugbild',
      type: 'image',
      options: {
        hotspot: true,
      },
      description: 'Optionales Bild des Fahrzeugs',
    },
    {
      name: 'isActive',
      title: 'Aktiv',
      type: 'boolean',
      initialValue: true,
      description: 'Fahrzeug im Konfigurator anzeigen',
    },
    {
      name: 'sortOrder',
      title: 'Sortierreihenfolge',
      type: 'number',
      description: 'Niedrigere Zahlen werden zuerst angezeigt',
    },
  ],
  preview: {
    select: {
      title: 'brand',
      subtitle: 'model',
      year: 'year',
      engine: 'engine',
      media: 'image',
    },
    prepare({ title, subtitle, year, engine, media }: any) {
      return {
        title: `${title || 'Unbekannte Marke'} ${subtitle || 'Unbekanntes Modell'}`,
        subtitle: `${year ? `${year} • ` : ''}${engine || 'Unbekannter Motor'}`,
        media,
      }
    },
  },
  orderings: [
    {
      title: 'Marke A-Z',
      name: 'brandAsc',
      by: [
        { field: 'brand', direction: 'asc' },
        { field: 'model', direction: 'asc' },
      ],
    },
    {
      title: 'Sortierreihenfolge',
      name: 'sortOrder',
      by: [{ field: 'sortOrder', direction: 'asc' }],
    },
  ],
}

export const schema: { types: SchemaTypeDefinition[] } = {
  types: [updatedSiteSettings, service, legalPage, servicesPage, homepage, tuningPage, vehicle],
}