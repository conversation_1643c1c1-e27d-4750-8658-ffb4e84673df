import React, { useState, useEffect, useCallback } from 'react'
import { <PERSON>, Stack, Text, TextInput, Button, Flex, <PERSON>ge, Spinner, Dialog } from '@sanity/ui'
import { SearchIcon, EditIcon } from '@sanity/icons'
import { createClient } from '@supabase/supabase-js'
// import { StageManager } from './StageManager'

// Supabase client setup
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'http://127.0.0.1:54321'
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0'

const supabase = createClient(supabaseUrl, supabaseKey)

interface Vehicle {
  id: string
  vehicle_type: string
  brand: string
  model: string
  year?: string
  motor?: string
  created_at: string
  updated_at?: string
  scraped_at?: string
  country?: string
  total_stages?: number
  sanity_id?: string
}

interface Stage {
  id: string
  vehicle_id: string
  name: string
  original_power?: number
  tuned_power?: number
  price_chf?: number
  description?: string
}

export function SupabaseVehicleManager() {
  const [vehicles, setVehicles] = useState<Vehicle[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('')
  const [selectedVehicle, setSelectedVehicle] = useState<Vehicle | null>(null)
  const [stages, setStages] = useState<Stage[]>([])
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalCount, setTotalCount] = useState(0)
  const [filters, setFilters] = useState({
    vehicleType: '',
    brand: ''
  })
  const [availableBrands, setAvailableBrands] = useState<string[]>([])
  const [availableTypes, setAvailableTypes] = useState<string[]>([])

  const ITEMS_PER_PAGE = 20

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm)
      if (searchTerm !== debouncedSearchTerm) {
        setCurrentPage(1) // Reset to first page when search changes
      }
    }, 300) // 300ms delay

    return () => clearTimeout(timer)
  }, [searchTerm, debouncedSearchTerm])

  // Fetch available filter options
  const fetchFilterOptions = useCallback(async () => {
    try {
      console.log('Loading filter options...')

      // Try RPC functions first, then fallback to direct queries
      let brandsData, typesData, brandsError, typesError

      try {
        const [brandsResult, typesResult] = await Promise.all([
          supabase.rpc('get_distinct_brands', { p_vehicle_type: null }),
          supabase.rpc('get_distinct_vehicle_types')
        ])
        brandsData = brandsResult.data
        brandsError = brandsResult.error
        typesData = typesResult.data
        typesError = typesResult.error
      } catch (rpcError) {
        console.log('RPC functions not available, using direct queries')
        brandsError = rpcError
        typesError = rpcError
      }

      let finalBrands: string[] = []
      let finalTypes: string[] = []

      if (brandsError) {
        console.error('Error fetching brands:', brandsError)
        // Fallback to direct query with higher limit
        const { data: fallbackBrands } = await supabase
          .from('vehicles')
          .select('brand')
          .not('brand', 'is', null)
          .order('brand')
          .limit(10000) // Much higher limit

        finalBrands = [...new Set(fallbackBrands?.map(v => v.brand) || [])]
      } else {
        finalBrands = [...new Set((brandsData as { brand: string }[])?.map(row => row.brand) || [])]
      }

      if (typesError) {
        console.error('Error fetching types:', typesError)
        // Fallback to direct query
        const { data: fallbackTypes } = await supabase
          .from('vehicles')
          .select('vehicle_type')
          .not('vehicle_type', 'is', null)
          .order('vehicle_type')
          .limit(100) // Types should be limited anyway

        finalTypes = [...new Set(fallbackTypes?.map(v => v.vehicle_type) || [])]
      } else {
        finalTypes = [...new Set((typesData as { vehicle_type: string }[])?.map(row => row.vehicle_type) || [])]
      }

      setAvailableBrands(finalBrands)
      setAvailableTypes(finalTypes)

      console.log(`Loaded ${finalBrands.length} brands and ${finalTypes.length} vehicle types`)
    } catch (error) {
      console.error('Error fetching filter options:', error)

      // Ultimate fallback - direct queries with high limits
      try {
        const [brandsResult, typesResult] = await Promise.all([
          supabase
            .from('vehicles')
            .select('brand')
            .not('brand', 'is', null)
            .order('brand')
            .limit(10000),
          supabase
            .from('vehicles')
            .select('vehicle_type')
            .not('vehicle_type', 'is', null)
            .order('vehicle_type')
            .limit(100)
        ])

        const uniqueBrands = [...new Set(brandsResult.data?.map(v => v.brand) || [])]
        const uniqueTypes = [...new Set(typesResult.data?.map(v => v.vehicle_type) || [])]

        setAvailableBrands(uniqueBrands)
        setAvailableTypes(uniqueTypes)
      } catch (fallbackError) {
        console.error('Fallback filter loading failed:', fallbackError)
      }
    }
  }, [])

  // Fetch brands for specific vehicle type
  const fetchBrandsForType = useCallback(async (vehicleType: string) => {
    try {
      console.log(`Loading brands for vehicle type: ${vehicleType}`)

      // Try RPC function first
      let brandsData, brandsError

      try {
        const result = await supabase.rpc('get_distinct_brands', { p_vehicle_type: vehicleType })
        brandsData = result.data
        brandsError = result.error
      } catch (rpcError) {
        console.log('RPC function not available, using direct query')
        brandsError = rpcError
      }

      let filteredBrands: string[] = []

      if (brandsError) {
        console.error('Error fetching brands for type:', brandsError)
        // Fallback to direct query
        const { data: fallbackBrands } = await supabase
          .from('vehicles')
          .select('brand')
          .eq('vehicle_type', vehicleType)
          .not('brand', 'is', null)
          .order('brand')
          .limit(10000)

        filteredBrands = [...new Set(fallbackBrands?.map(v => v.brand) || [])]
      } else {
        filteredBrands = [...new Set((brandsData as { brand: string }[])?.map(row => row.brand) || [])]
      }

      setAvailableBrands(filteredBrands)

      // Reset brand filter if current selection is not available for this type
      if (filters.brand && !filteredBrands.includes(filters.brand)) {
        setFilters(prev => ({ ...prev, brand: '' }))
      }

      console.log(`Loaded ${filteredBrands.length} brands for ${vehicleType}`)
    } catch (error) {
      console.error('Error fetching brands for vehicle type:', error)
    }
  }, [filters.brand])

  // Fetch vehicles with pagination and search - handles Supabase 1000 row limit
  const fetchVehicles = useCallback(async () => {
    setLoading(true)
    try {
      console.log('Fetching vehicles with:', { debouncedSearchTerm, filters, currentPage })

      // Strategy 1: If we have search term, use text search which can handle more than 1000 rows
      if (debouncedSearchTerm.trim()) {
        await fetchVehiclesWithSearch()
        return
      }

      // Strategy 2: Regular query for browsing without search
      let query = supabase
        .from('vehicles')
        .select('*', { count: 'exact' })
        .order('brand', { ascending: true })
        .order('model', { ascending: true })

      // Apply type filter
      if (filters.vehicleType) {
        console.log('Applying vehicle type filter:', filters.vehicleType)
        query = query.eq('vehicle_type', filters.vehicleType)
      }

      // Apply brand filter
      if (filters.brand) {
        console.log('Applying brand filter:', filters.brand)
        query = query.eq('brand', filters.brand)
      }

      // Apply pagination last
      query = query.range((currentPage - 1) * ITEMS_PER_PAGE, currentPage * ITEMS_PER_PAGE - 1)

      const { data, error, count } = await query

      if (error) {
        console.error('Supabase query error:', error)
        throw error
      }

      console.log(`Found ${count} vehicles, showing ${data?.length} on page ${currentPage}`)

      setVehicles(data || [])
      setTotalCount(count || 0)
    } catch (error) {
      console.error('Error fetching vehicles:', error)
      setVehicles([])
      setTotalCount(0)
    } finally {
      setLoading(false)
    }
  }, [currentPage, debouncedSearchTerm, filters, fetchVehiclesWithSearch])

  // Special search function that handles 1000+ row limit
  const fetchVehiclesWithSearch = useCallback(async () => {
    try {
      const searchValue = debouncedSearchTerm.trim()
      console.log('Using advanced search for:', searchValue)

      // Strategy 1: Try RPC function for full-text search (if available)
      try {
        const { data: rpcData, error: rpcError } = await supabase
          .rpc('search_vehicles', {
            search_term: searchValue,
            vehicle_type_filter: filters.vehicleType || null,
            brand_filter: filters.brand || null,
            limit_count: ITEMS_PER_PAGE,
            offset_count: (currentPage - 1) * ITEMS_PER_PAGE
          })

        if (!rpcError && rpcData) {
          console.log('RPC search successful:', rpcData.length, 'results')
          setVehicles(rpcData)
          setTotalCount(rpcData.length < ITEMS_PER_PAGE ? rpcData.length : 1000) // Estimate
          return
        }
      } catch {
        console.log('RPC search not available, using fallback')
      }

      // Strategy 2: Smart sequential search to overcome 1000 limit
      console.log('Using smart sequential search...')

      const allVehicles = new Map()

      // Search strategy: prioritize by relevance
      const searchStrategies = [
        { field: 'brand', weight: 4 },
        { field: 'model', weight: 3 },
        { field: 'engine', weight: 2 },
        { field: 'motor', weight: 1 }
      ]

      for (const strategy of searchStrategies) {
        try {
          let query = supabase
            .from('vehicles')
            .select('*')
            .ilike(strategy.field, `%${searchValue}%`)
            .order('brand', { ascending: true })
            .order('model', { ascending: true })
            .limit(800) // Conservative limit per field

          // Apply filters
          if (filters.vehicleType) {
            query = query.eq('vehicle_type', filters.vehicleType)
          }
          if (filters.brand) {
            query = query.eq('brand', filters.brand)
          }

          const { data, error } = await query

          if (!error && data) {
            console.log(`Found ${data.length} matches in ${strategy.field}`)
            data.forEach(vehicle => {
              if (!allVehicles.has(vehicle.id)) {
                allVehicles.set(vehicle.id, { ...vehicle, searchWeight: strategy.weight })
              } else {
                // Increase weight for vehicles found in multiple fields
                const existing = allVehicles.get(vehicle.id)
                existing.searchWeight += strategy.weight
              }
            })
          }

          // If we have enough results, we can stop early
          if (allVehicles.size >= ITEMS_PER_PAGE * 3) {
            console.log('Early termination: found enough results')
            break
          }
        } catch (fieldError) {
          console.error(`Error searching ${strategy.field}:`, fieldError)
        }
      }

      const uniqueVehicles = Array.from(allVehicles.values())
      console.log(`Smart search found ${uniqueVehicles.length} unique vehicles`)

      // Sort by relevance (search weight) then alphabetically
      uniqueVehicles.sort((a, b) => {
        if (a.searchWeight !== b.searchWeight) {
          return b.searchWeight - a.searchWeight // Higher weight first
        }
        if (a.brand !== b.brand) return a.brand.localeCompare(b.brand)
        return a.model.localeCompare(b.model)
      })

      // Apply pagination to combined results
      const startIndex = (currentPage - 1) * ITEMS_PER_PAGE
      const endIndex = startIndex + ITEMS_PER_PAGE
      const paginatedVehicles = uniqueVehicles.slice(startIndex, endIndex)

      // Remove searchWeight from final results
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const cleanedVehicles = paginatedVehicles.map(({ searchWeight, ...vehicle }) => vehicle)

      setVehicles(cleanedVehicles)
      setTotalCount(uniqueVehicles.length)

      console.log(`Showing ${cleanedVehicles.length} vehicles on page ${currentPage} of ${Math.ceil(uniqueVehicles.length / ITEMS_PER_PAGE)}`)

    } catch (error) {
      console.error('Advanced search failed:', error)
      setVehicles([])
      setTotalCount(0)
    }
  }, [debouncedSearchTerm, filters, currentPage])

  // Fetch stages for selected vehicle
  const fetchStages = async (vehicleId: string) => {
    try {
      const { data, error } = await supabase
        .from('stages')
        .select('*')
        .eq('vehicle_id', vehicleId)
        .order('name', { ascending: true })

      if (error) throw error
      setStages(data || [])
    } catch (error) {
      console.error('Error fetching stages:', error)
      setStages([])
    }
  }

  // Open edit dialog
  const openEditDialog = async (vehicle: Vehicle) => {
    setSelectedVehicle(vehicle)
    await fetchStages(vehicle.id)
    setIsEditDialogOpen(true)
  }

  // Close edit dialog
  const closeEditDialog = () => {
    setSelectedVehicle(null)
    setStages([])
    setIsEditDialogOpen(false)
  }

  // Update vehicle
  const updateVehicle = async (updatedVehicle: Partial<Vehicle>) => {
    if (!selectedVehicle) return

    try {
      const { error } = await supabase
        .from('vehicles')
        .update(updatedVehicle)
        .eq('id', selectedVehicle.id)

      if (error) throw error

      // Refresh vehicles list
      await fetchVehicles()
      closeEditDialog()
    } catch (error) {
      console.error('Error updating vehicle:', error)
      alert('Fehler beim Aktualisieren des Fahrzeugs')
    }
  }

  useEffect(() => {
    fetchFilterOptions()
  }, [fetchFilterOptions])

  // Update available brands when vehicle type changes
  useEffect(() => {
    if (filters.vehicleType) {
      fetchBrandsForType(filters.vehicleType)
    } else {
      fetchFilterOptions() // Reset to all brands
    }
  }, [filters.vehicleType, fetchBrandsForType, fetchFilterOptions])

  useEffect(() => {
    fetchVehicles()
  }, [currentPage, debouncedSearchTerm, filters, fetchVehicles])

  const totalPages = Math.ceil(totalCount / ITEMS_PER_PAGE)

  return (
    <Stack space={4} padding={4}>
      {/* Header */}
      <Card padding={4} tone="primary">
        <Stack space={3}>
          <Text size={4} weight="bold">
            🗄️ Fahrzeug-Datenbank (Supabase)
          </Text>
          <Text size={1} muted>
            Verwalte alle {totalCount.toLocaleString()} Fahrzeuge direkt aus Sanity Studio
          </Text>
        </Stack>
      </Card>

      {/* Search and Filters */}
      <Card padding={4}>
        <Stack space={3}>
          <Flex gap={3} wrap="wrap" align="flex-end">
            <Stack space={2} flex={1} style={{ minWidth: '300px' }}>
              <Text size={1} weight="medium">Suche</Text>
              <TextInput
                icon={SearchIcon}
                placeholder="Suche nach Marke, Modell oder Motor..."
                value={searchTerm}
                onChange={(event) => {
                  setSearchTerm(event.currentTarget.value)
                  // Page reset is handled by debouncing effect
                }}
              />
            </Stack>

            <Stack space={2} style={{ minWidth: '150px' }}>
              <Text size={1} weight="medium">Fahrzeugtyp</Text>
              <select
                value={filters.vehicleType}
                onChange={(e) => {
                  setFilters({...filters, vehicleType: e.target.value})
                  setCurrentPage(1)
                }}
                style={{
                  padding: '8px 12px',
                  border: '1px solid var(--card-border-color)',
                  borderRadius: '4px',
                  fontSize: '14px',
                  backgroundColor: 'var(--card-bg-color)',
                  minHeight: '36px'
                }}
              >
                <option value="">Alle Typen</option>
                {availableTypes.map(type => (
                  <option key={type} value={type}>
                    {type}
                  </option>
                ))}
              </select>
            </Stack>

            <Stack space={2} style={{ minWidth: '150px' }}>
              <Text size={1} weight="medium">
                Marke {filters.vehicleType && `(${filters.vehicleType})`}
              </Text>
              <select
                value={filters.brand}
                onChange={(e) => {
                  setFilters({...filters, brand: e.target.value})
                  setCurrentPage(1)
                }}
                style={{
                  padding: '8px 12px',
                  border: '1px solid var(--card-border-color)',
                  borderRadius: '4px',
                  fontSize: '14px',
                  backgroundColor: 'var(--card-bg-color)',
                  minHeight: '36px'
                }}
              >
                <option value="">
                  {filters.vehicleType
                    ? `Alle ${filters.vehicleType}-Marken`
                    : 'Alle Marken'
                  }
                </option>
                {availableBrands.map(brand => (
                  <option key={brand} value={brand}>{brand}</option>
                ))}
              </select>
              {filters.vehicleType && (
                <Text size={0} muted>
                  {availableBrands.length} Marken verfügbar
                </Text>
              )}
            </Stack>

            <Button
              text="Zurücksetzen"
              mode="ghost"
              onClick={() => {
                setSearchTerm('')
                setDebouncedSearchTerm('')
                setFilters({ vehicleType: '', brand: '' })
                setCurrentPage(1)
              }}
              style={{ minHeight: '36px' }}
            />
          </Flex>
        </Stack>
      </Card>

      {/* Results */}
      <Card>
        {loading ? (
          <Flex justify="center" padding={6}>
            <Spinner muted />
          </Flex>
        ) : (
          <Stack space={0}>
            {/* Results header */}
            <Card padding={3} tone="transparent" style={{ borderBottom: '1px solid var(--card-border-color)' }}>
              <Flex justify="space-between" align="center">
                <Text size={1} muted>
                  {totalCount.toLocaleString()} Fahrzeuge gefunden
                </Text>
                <Text size={1} muted>
                  Seite {currentPage} von {totalPages}
                </Text>
              </Flex>
            </Card>

            {/* Vehicle list */}
            {vehicles.map((vehicle) => (
              <Card
                key={vehicle.id}
                padding={3}
                style={{ borderBottom: '1px solid var(--card-border-color)' }}
                tone="transparent"
              >
                <Flex justify="space-between" align="center">
                  <Stack space={2} flex={1}>
                    <Flex gap={2} align="center">
                      <Text weight="medium">
                        {vehicle.brand} {vehicle.model}
                      </Text>
                      {vehicle.year && (
                        <Badge tone="primary" mode="outline">
                          {vehicle.year}
                        </Badge>
                      )}
                      <Badge tone="default" mode="outline">
                        {vehicle.vehicle_type}
                      </Badge>
                    </Flex>
                    <Text size={1} muted>
                      {vehicle.motor || 'Motor nicht angegeben'}
                    </Text>
                  </Stack>
                  <Button
                    icon={EditIcon}
                    mode="ghost"
                    onClick={() => openEditDialog(vehicle)}
                    text="Bearbeiten"
                  />
                </Flex>
              </Card>
            ))}
          </Stack>
        )}
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <Card padding={3}>
          <Flex justify="center" gap={2}>
            <Button
              text="Vorherige"
              mode="ghost"
              disabled={currentPage === 1}
              onClick={() => setCurrentPage(currentPage - 1)}
            />
            <Text size={1} style={{ padding: '8px 16px' }}>
              {currentPage} / {totalPages}
            </Text>
            <Button
              text="Nächste"
              mode="ghost"
              disabled={currentPage === totalPages}
              onClick={() => setCurrentPage(currentPage + 1)}
            />
          </Flex>
        </Card>
      )}

      {/* Edit Dialog */}
      {isEditDialogOpen && selectedVehicle && (
        <EditVehicleDialog
          vehicle={selectedVehicle}
          stages={stages}
          onClose={closeEditDialog}
          onSave={updateVehicle}
          availableTypes={availableTypes}
        />
      )}
    </Stack>
  )
}

// Edit Dialog Component
function EditVehicleDialog({
  vehicle,
  stages,
  onClose,
  onSave,
  availableTypes
}: {
  vehicle: Vehicle
  stages: Stage[]
  onClose: () => void
  onSave: (vehicle: Partial<Vehicle>) => void
  availableTypes: string[]
}) {
  const [formData, setFormData] = useState({
    brand: vehicle.brand || '',
    model: vehicle.model || '',
    year: vehicle.year || '',
    motor: vehicle.motor || '',
    vehicle_type: vehicle.vehicle_type || ''
  })

  const handleSave = () => {
    onSave({
      brand: formData.brand,
      model: formData.model,
      year: formData.year,
      motor: formData.motor,
      vehicle_type: formData.vehicle_type
    })
  }

  return (
    <Dialog
      header={`${vehicle.brand} ${vehicle.model} bearbeiten`}
      id="edit-vehicle"
      onClose={onClose}
      width={1}
    >
      <Card padding={4}>
        <Stack space={4}>
          {/* Vehicle Details */}
          <Stack space={3}>
            <Text size={2} weight="medium">Fahrzeug-Details</Text>

            <Flex gap={3}>
              <Stack space={2} flex={1}>
                <Text size={1} weight="medium">Marke</Text>
                <TextInput
                  value={formData.brand}
                  onChange={(e) => setFormData({...formData, brand: e.currentTarget.value})}
                />
              </Stack>

              <Stack space={2} flex={1}>
                <Text size={1} weight="medium">Modell</Text>
                <TextInput
                  value={formData.model}
                  onChange={(e) => setFormData({...formData, model: e.currentTarget.value})}
                />
              </Stack>
            </Flex>

            <Flex gap={3}>
              <Stack space={2} flex={1}>
                <Text size={1} weight="medium">Baujahr</Text>
                <TextInput
                  value={formData.year}
                  onChange={(e) => setFormData({...formData, year: e.currentTarget.value})}
                />
              </Stack>

              <Stack space={2} flex={1}>
                <Text size={1} weight="medium">Motor</Text>
                <TextInput
                  value={formData.motor}
                  onChange={(e) => setFormData({...formData, motor: e.currentTarget.value})}
                />
              </Stack>
            </Flex>

            <Stack space={2}>
              <Text size={1} weight="medium">Fahrzeugtyp</Text>
              <select
                value={formData.vehicle_type}
                onChange={(e) => setFormData({...formData, vehicle_type: e.target.value})}
                style={{
                  padding: '8px 12px',
                  border: '1px solid var(--card-border-color)',
                  borderRadius: '4px',
                  fontSize: '14px'
                }}
              >
                <option value="">Typ wählen...</option>
                {availableTypes.map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </Stack>
          </Stack>

          {/* Stages */}
          <Stack space={3}>
            <Text size={2} weight="medium">
              Tuning-Stages ({stages.length})
            </Text>

            {stages.length > 0 ? (
              <Stack space={2}>
                {stages.map((stage) => (
                  <Card key={stage.id} padding={3} tone="transparent" style={{ border: '1px solid var(--card-border-color)' }}>
                    <Stack space={2}>
                      <Flex justify="space-between" align="center">
                        <Text weight="medium">{stage.name}</Text>
                        <Badge tone="positive">
                          {stage.tuned_power ? `${stage.tuned_power} PS` : 'N/A'}
                        </Badge>
                      </Flex>
                      {stage.description && (
                        <Text size={1} muted>{stage.description}</Text>
                      )}
                      {stage.price_chf && (
                        <Text size={1}>
                          <strong>Preis:</strong> CHF {stage.price_chf.toLocaleString()}
                        </Text>
                      )}
                    </Stack>
                  </Card>
                ))}
              </Stack>
            ) : (
              <Card padding={3} tone="caution">
                <Text size={1} muted>Keine Tuning-Stages verfügbar</Text>
              </Card>
            )}
          </Stack>

          {/* Actions */}
          <Flex gap={2} justify="flex-end">
            <Button text="Abbrechen" mode="ghost" onClick={onClose} />
            <Button text="Speichern" tone="primary" onClick={handleSave} />
          </Flex>
        </Stack>
      </Card>
    </Dialog>
  )
}
