import { type SchemaTypeDefinition, defineField, defineType } from 'sanity'
import type { Rule } from 'sanity'

// Homepage schema
const homepage: SchemaTypeDefinition = {
  name: 'homepage',
  title: 'Homepage',
  type: 'document',
  fields: [
    {
      name: 'heroT<PERSON><PERSON>',
      title: 'Hero Title',
      type: 'string',
      validation: (Rule: Rule) => Rule.required(),
      initialValue: 'Professionelles Fahrzeug-Tuning',
    },
    {
      name: 'heroSubtitle',
      title: 'Hero Subtitle',
      type: 'string',
      validation: (Rule: Rule) => Rule.required(),
      initialValue: 'Maximale Leistung, optimaler Verbrauch',
    },
    {
      name: 'heroDescription',
      title: 'Hero Description',
      type: 'text',
      rows: 3,
      validation: (Rule: Rule) => Rule.required(),
      initialValue: 'Entdecken Sie das volle Potenzial Ihres Fahrzeugs mit unserer professionellen Kennfeldoptimierung und Leistungssteigerung.',
    },
    {
      name: 'ctaButtonText',
      title: 'Primary CTA Button Text',
      type: 'string',
      validation: (Rule: Rule) => Rule.required(),
      initialValue: 'Konfigurator starten',
    },
    {
      name: 'ctaSecondaryText',
      title: 'Secondary CTA Button Text',
      type: 'string',
      validation: (Rule: Rule) => Rule.required(),
      initialValue: 'Unsere Services',
    },
    {
      name: 'features',
      title: 'Features',
      type: 'array',
      of: [
        {
          type: 'object',
          name: 'feature',
          title: 'Feature',
          fields: [
            {
              name: 'title',
              title: 'Title',
              type: 'string',
              validation: (Rule: Rule) => Rule.required(),
            },
            {
              name: 'description',
              title: 'Description',
              type: 'text',
              rows: 2,
              validation: (Rule: Rule) => Rule.required(),
            },
            {
              name: 'icon',
              title: 'Icon',
              type: 'string',
              options: {
                list: [
                  { title: 'Chart', value: 'chart' },
                  { title: 'Shield', value: 'shield' },
                  { title: 'Users', value: 'users' },
                  { title: 'Lightning', value: 'lightning' },
                  { title: 'Cog', value: 'cog' },
                  { title: 'Clock', value: 'clock' },
                ],
              },
              validation: (Rule: Rule) => Rule.required(),
              initialValue: 'chart',
            },
            {
              name: 'order',
              title: 'Display Order',
              type: 'number',
              validation: (Rule: Rule) => Rule.required(),
            },
          ],
        },
      ],
      validation: (Rule: Rule) => Rule.min(3).max(6),
    },
  ],
}

// Tuning page schema
const tuningPage: SchemaTypeDefinition = {
  name: 'tuningPage',
  title: 'Tuning Page',
  type: 'document',
  fields: [
    {
      name: 'pageTitle',
      title: 'Page Title',
      type: 'string',
      validation: (Rule: Rule) => Rule.required(),
      initialValue: 'Tuning-Konfigurator',
    },
    {
      name: 'pageSubtitle',
      title: 'Page Subtitle',
      type: 'string',
      initialValue: 'Entdecken Sie das Potenzial Ihres Fahrzeugs',
    },
    {
      name: 'configuratorSettings',
      title: 'Configurator Settings',
      type: 'object',
      fields: [
        {
          name: 'loadingText',
          title: 'Loading Text',
          type: 'string',
          initialValue: 'Lade Konfigurator...',
        },
        {
          name: 'introText',
          title: 'Introduction Text',
          type: 'text',
          rows: 3,
        },
        {
          name: 'defaultVehicleType',
          title: 'Default Vehicle Type',
          type: 'string',
          initialValue: 'Kraftfahrzeuge (PKW)',
        },
        {
          name: 'pricePrefix',
          title: 'Price Prefix (e.g., "Ab")',
          type: 'string',
          initialValue: 'Ab',
        },
        {
          name: 'priceSuffix',
          title: 'Price Suffix (e.g., "CHF")',
          type: 'string',
          initialValue: 'CHF',
        },
        {
          name: 'priceIncludesTax',
          title: 'Price Includes Tax Text',
          type: 'string',
          initialValue: 'inkl. MwSt.',
        },
        {
          name: 'minSpecsDisclaimer',
          title: 'Minimum Specifications Disclaimer',
          type: 'string',
          initialValue: 'Mindestangaben',
        },
      ],
    },
    {
      name: 'labels',
      title: 'UI Labels',
      type: 'object',
      fields: [
        {
          name: 'vehicleTypeLabel',
          title: 'Vehicle Type Label',
          type: 'string',
          initialValue: 'Fahrzeugtyp',
        },
        {
          name: 'brandLabel',
          title: 'Brand Label',
          type: 'string',
          initialValue: 'Marke',
        },
        {
          name: 'modelLabel',
          title: 'Model Label',
          type: 'string',
          initialValue: 'Modell',
        },
        {
          name: 'yearLabel',
          title: 'Year Label',
          type: 'string',
          initialValue: 'Baujahr',
        },
        {
          name: 'motorLabel',
          title: 'Motor Label',
          type: 'string',
          initialValue: 'Motor',
        },
        {
          name: 'selectPlaceholder',
          title: 'Select Placeholder',
          type: 'string',
          initialValue: 'Wählen Sie...',
        },
        {
          name: 'performanceTitle',
          title: 'Performance Section Title',
          type: 'string',
          initialValue: 'Leistungssteigerung',
        },
        {
          name: 'originalLabel',
          title: 'Original Value Label',
          type: 'string',
          initialValue: 'Original',
        },
        {
          name: 'tunedLabel',
          title: 'Tuned Value Label',
          type: 'string',
          initialValue: 'Getunt',
        },
        {
          name: 'differenceLabel',
          title: 'Difference Label',
          type: 'string',
          initialValue: 'Unterschied',
        },
      ],
    },
  ],
}

// Site settings schema
const siteSettings: SchemaTypeDefinition = {
  name: 'siteSettings',
  title: 'Site Settings',
  type: 'document',
  fields: [
    {
      name: 'siteName',
      title: 'Site Name',
      type: 'string',
      validation: (Rule: Rule) => Rule.required(),
      initialValue: 'DB-Performance',
    },
    {
      name: 'siteDescription',
      title: 'Site Description',
      type: 'text',
      rows: 3,
    },
    {
      name: 'logo',
      title: 'Logo',
      type: 'image',
      options: {
        hotspot: true,
      },
    },
    {
      name: 'navigation',
      title: 'Navigation',
      type: 'object',
      fields: [
        {
          name: 'homeLabel',
          title: 'Home Label',
          type: 'string',
          initialValue: 'Home',
        },
        {
          name: 'servicesLabel',
          title: 'Services Label',
          type: 'string',
          initialValue: 'Services',
        },
        {
          name: 'tuningLabel',
          title: 'Tuning Label',
          type: 'string',
          initialValue: 'Tuning-Konfigurator',
        },
        {
          name: 'detailingLabel',
          title: 'Fahrzeugaufbereitung Label',
          type: 'string',
          initialValue: 'Fahrzeugaufbereitung',
        },
        {
          name: 'contactLabel',
          title: 'Contact Label',
          type: 'string',
          initialValue: 'Kontakt',
        },
      ],
    },
    {
      name: 'footer',
      title: 'Footer',
      type: 'object',
      fields: [
        {
          name: 'companyName',
          title: 'Company Name',
          type: 'string',
          validation: (Rule: Rule) => Rule.required(),
          initialValue: 'DB-Performance Garage Bytyci',
        },
        {
          name: 'address',
          title: 'Address',
          type: 'text',
          rows: 3,
          initialValue: 'Stauseestrasse 1, 5316 Leuggern, Schweiz',
        },
        {
          name: 'rightsText',
          title: 'Rights Text',
          type: 'string',
          initialValue: 'Alle Rechte vorbehalten',
        },
        {
          name: 'legalSectionTitle',
          title: 'Legal Section Title',
          type: 'string',
          initialValue: 'Rechtliches',
        },
        {
          name: 'contactSectionTitle',
          title: 'Contact Section Title',
          type: 'string',
          initialValue: 'Kontakt',
        },
        {
          name: 'phone',
          title: 'Phone Number',
          type: 'string',
          initialValue: '+41 XX XXX XX XX',
        },
        {
          name: 'email',
          title: 'Email Address',
          type: 'string',
          validation: (Rule: Rule) => Rule.email(),
          initialValue: '<EMAIL>',
        },
        {
          name: 'whatsapp',
          title: 'WhatsApp Number',
          type: 'string',
          description: 'WhatsApp Business number in international format (e.g., +41XXXXXXXXX)',
          initialValue: '+41XXXXXXXXX',
        },
        {
          name: 'legalLinks',
          title: 'Legal Links',
          type: 'object',
          fields: [
            {
              name: 'imprintText',
              title: 'Impressum Link Text',
              type: 'string',
              initialValue: 'Impressum',
            },
            {
              name: 'privacyText',
              title: 'Privacy Policy Link Text',
              type: 'string',
              initialValue: 'Datenschutz',
            },
            {
              name: 'termsText',
              title: 'Terms Link Text',
              type: 'string',
              initialValue: 'AGB',
            },
          ],
        },
      ],
    },
  ],
}

// Service schema
const service: SchemaTypeDefinition = {
  name: 'service',
  title: 'Service',
  type: 'document',
  fields: [
    {
      name: 'title',
      title: 'Title',
      type: 'string',
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'title',
        maxLength: 96,
      },
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'description',
      title: 'Description',
      type: 'text',
      rows: 4,
    },
    {
      name: 'content',
      title: 'Content',
      type: 'array',
      of: [
        {
          type: 'block',
        },
        {
          type: 'image',
          options: {
            hotspot: true,
          },
          fields: [
            {
              name: 'alt',
              title: 'Alt Text',
              type: 'string',
            },
          ],
        },
      ],
    },
    {
      name: 'image',
      title: 'Featured Image',
      type: 'image',
      options: {
        hotspot: true,
      },
      fields: [
        {
          name: 'alt',
          title: 'Alt Text',
          type: 'string',
        },
      ],
    },
    {
      name: 'category',
      title: 'Category',
      type: 'string',
      options: {
        list: [
          { title: 'Reparatur', value: 'repair' },
          { title: 'Aufbereitung', value: 'detailing' },
          { title: 'Software-Tuning', value: 'tuning' },
        ],
      },
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'order',
      title: 'Display Order',
      type: 'number',
      initialValue: 0,
    },
  ],
  orderings: [
    {
      title: 'Display Order',
      name: 'orderAsc',
      by: [{ field: 'order', direction: 'asc' }],
    },
  ],
}

// Services page schema
const servicesPage: SchemaTypeDefinition = {
  name: 'servicesPage',
  title: 'Services Page',
  type: 'document',
  fields: [
    {
      name: 'heroTitle',
      title: 'Hero Title',
      type: 'string',
      initialValue: 'Unsere Dienstleistungen',
    },
    {
      name: 'heroSubtitle',
      title: 'Hero Subtitle',
      type: 'string',
      initialValue: 'Professionelle Fahrzeugoptimierung auf höchstem Niveau',
    },
    {
      name: 'heroDescription',
      title: 'Hero Description',
      type: 'text',
      rows: 3,
      initialValue: 'Von der Kennfeldoptimierung bis zur kompletten Leistungssteigerung - wir bieten individuelle Lösungen für Ihr Fahrzeug.',
    },
    {
      name: 'services',
      title: 'Services',
      type: 'array',
      of: [
        {
          type: 'object',
          name: 'serviceCard',
          title: 'Service Card',
          fields: [
            {
              name: 'title',
              title: 'Title',
              type: 'string',
              validation: (Rule: Rule) => Rule.required(),
            },
            {
              name: 'description',
              title: 'Description',
              type: 'text',
              rows: 3,
              validation: (Rule: Rule) => Rule.required(),
            },
            {
              name: 'features',
              title: 'Features',
              type: 'array',
              of: [{ type: 'string' }],
              validation: (Rule: Rule) => Rule.min(1).max(5),
            },
            {
              name: 'buttonText',
              title: 'Button Text',
              type: 'string',
              validation: (Rule: Rule) => Rule.required(),
            },
            {
              name: 'buttonLink',
              title: 'Button Link',
              type: 'string',
              description: 'Internal link (e.g., /tuning) or external URL',
            },
            {
              name: 'isExternalLink',
              title: 'Is External Link',
              type: 'boolean',
              initialValue: false,
            },
            {
              name: 'color',
              title: 'Card Color',
              type: 'string',
              options: {
                list: [
                  { title: 'Blue', value: 'blue' },
                  { title: 'Green', value: 'green' },
                  { title: 'Orange', value: 'orange' },
                  { title: 'Purple', value: 'purple' },
                  { title: 'Red', value: 'red' },
                  { title: 'Teal', value: 'teal' },
                ],
              },
              initialValue: 'blue',
            },
            {
              name: 'icon',
              title: 'Icon',
              type: 'string',
              options: {
                list: [
                  { title: 'Lightning (Chiptuning)', value: 'lightning' },
                  { title: 'Chart (Leistungsmessung)', value: 'chart' },
                  { title: 'Cog (DPF)', value: 'cog' },
                  { title: 'Clock (Motorrad)', value: 'clock' },
                  { title: 'Beaker (Sonderfahrzeuge)', value: 'beaker' },
                  { title: 'Chat (Beratung)', value: 'chat' },
                ],
              },
              initialValue: 'lightning',
            },
            {
              name: 'order',
              title: 'Display Order',
              type: 'number',
              initialValue: 0,
            },
          ],
        },
      ],
      validation: (Rule: Rule) => Rule.min(1).max(6),
    },
    {
      name: 'ctaTitle',
      title: 'CTA Title',
      type: 'string',
      initialValue: 'Bereit für mehr Leistung?',
    },
    {
      name: 'ctaDescription',
      title: 'CTA Description',
      type: 'text',
      rows: 2,
      initialValue: 'Entdecken Sie das Potenzial Ihres Fahrzeugs mit unserem Tuning-Konfigurator',
    },
    {
      name: 'ctaButtonText',
      title: 'CTA Button Text',
      type: 'string',
      initialValue: 'Konfigurator starten',
    },
  ],
}

// Legal page schema
const legalPage: SchemaTypeDefinition = {
  name: 'legalPage',
  title: 'Legal Page',
  type: 'document',
  fields: [
    {
      name: 'title',
      title: 'Title',
      type: 'string',
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'title',
        maxLength: 96,
      },
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'content',
      title: 'Content',
      type: 'array',
      of: [
        {
          type: 'block',
        },
      ],
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'lastUpdated',
      title: 'Last Updated',
      type: 'datetime',
      initialValue: () => new Date().toISOString(),
    },
  ],
}

// Detailing Page Schema
const detailingPageSchema = defineType({
  name: 'detailingPage',
  title: 'Fahrzeugaufbereitung Seite',
  type: 'document',
  fields: [
    defineField({
      name: 'title',
      title: 'Page Title',
      type: 'string',
      validation: Rule => Rule.required(),
      initialValue: 'Fahrzeugaufbereitung',
    }),
    defineField({
      name: 'heroSection',
      title: 'Hero Section',
      type: 'object',
      fields: [
        defineField({
          name: 'heroTitle',
          title: 'Hero Title',
          type: 'string',
          initialValue: 'Professionelle Fahrzeugaufbereitung',
        }),
        defineField({
          name: 'heroSubtitle',
          title: 'Hero Subtitle',
          type: 'string',
          initialValue: 'Perfekte Pflege für Ihr Fahrzeug',
        }),
        defineField({
          name: 'heroDescription',
          title: 'Hero Description',
          type: 'text',
          initialValue: 'Wir bieten professionelle Fahrzeugaufbereitung mit modernsten Techniken und Produkten.',
        }),
      ],
    }),
    defineField({
      name: 'services',
      title: 'Detailing Services',
      type: 'array',
      of: [
        {
          type: 'object',
          fields: [
            defineField({
              name: 'title',
              title: 'Service Title',
              type: 'string',
              validation: Rule => Rule.required(),
            }),
            defineField({
              name: 'description',
              title: 'Service Description',
              type: 'text',
              validation: Rule => Rule.required(),
            }),
            defineField({
              name: 'features',
              title: 'Service Features',
              type: 'array',
              of: [{ type: 'string' }],
              validation: Rule => Rule.required().min(1),
            }),
            defineField({
              name: 'buttonText',
              title: 'Button Text',
              type: 'string',
              initialValue: 'Termin vereinbaren',
            }),
            defineField({
              name: 'buttonLink',
              title: 'Button Link',
              type: 'string',
              initialValue: 'mailto:<EMAIL>',
            }),
            defineField({
              name: 'isExternalLink',
              title: 'Is External Link',
              type: 'boolean',
              initialValue: true,
            }),
            defineField({
              name: 'color',
              title: 'Card Color',
              type: 'string',
              options: {
                list: [
                  { title: 'Blue', value: 'blue' },
                  { title: 'Green', value: 'green' },
                  { title: 'Orange', value: 'orange' },
                  { title: 'Purple', value: 'purple' },
                  { title: 'Red', value: 'red' },
                  { title: 'Teal', value: 'teal' },
                ],
              },
              initialValue: 'blue',
            }),
            defineField({
              name: 'icon',
              title: 'Service Icon',
              type: 'string',
              options: {
                list: [
                  { title: 'Polish', value: 'polish' },
                  { title: 'Shield', value: 'shield' },
                  { title: 'Sparkles', value: 'sparkles' },
                  { title: 'Car', value: 'car' },
                ],
              },
              initialValue: 'sparkles',
            }),
            defineField({
              name: 'order',
              title: 'Display Order',
              type: 'number',
              validation: Rule => Rule.required().min(1),
            }),
          ],
          preview: {
            select: {
              title: 'title',
              subtitle: 'description',
              order: 'order',
            },
            prepare(selection) {
              const { title, subtitle, order } = selection;
              return {
                title: `${order}. ${title}`,
                subtitle: subtitle,
              };
            },
          },
        },
      ],
      validation: Rule => Rule.required().min(1),
    }),
    defineField({
      name: 'ctaSection',
      title: 'Call-to-Action Section',
      type: 'object',
      fields: [
        defineField({
          name: 'ctaTitle',
          title: 'CTA Title',
          type: 'string',
          initialValue: 'Bereit für eine professionelle Aufbereitung?',
        }),
        defineField({
          name: 'ctaDescription',
          title: 'CTA Description',
          type: 'text',
          initialValue: 'Kontaktieren Sie uns für ein unverbindliches Angebot.',
        }),
        defineField({
          name: 'ctaButtonText',
          title: 'CTA Button Text',
          type: 'string',
          initialValue: 'Jetzt Termin vereinbaren',
        }),
      ],
    }),
    defineField({
      name: 'lastUpdated',
      title: 'Last Updated',
      type: 'datetime',
      readOnly: true,
      initialValue: () => new Date().toISOString(),
    }),
  ],
  preview: {
    select: {
      title: 'title',
    },
    prepare(selection) {
      return {
        title: selection.title || 'Fahrzeugaufbereitung Seite',
      };
    },
  },
});

export const schema: { types: SchemaTypeDefinition[] } = {
  types: [siteSettings, servicesPage, service, legalPage, homepage, tuningPage, detailingPageSchema],
}
