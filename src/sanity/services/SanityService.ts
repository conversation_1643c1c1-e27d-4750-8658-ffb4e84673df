import { createClient } from '@sanity/client'
import { groq } from 'next-sanity'
import type { SanityClient } from '@sanity/client'
import { 
  siteSettingsQuery,
  homepageQuery,
  servicesPageQuery,
  tuningPageQuery,
  detailingPageQuery
} from '../lib/queries'
import type { 
  SiteSettingsQueryResult, 
  HomepageQueryResult, 
  ServicesPageQueryResult,
  TuningPageQueryResult,
  DetailingPageQueryResult
} from '../types'

// Environment variables validation
const projectId = process.env.NEXT_PUBLIC_SANITY_PROJECT_ID
const dataset = process.env.NEXT_PUBLIC_SANITY_DATASET || 'production'
const apiVersion = process.env.NEXT_PUBLIC_SANITY_API_VERSION || '2024-01-01'
const token = process.env.SANITY_API_READ_TOKEN

if (!projectId) {
  console.warn('Missing NEXT_PUBLIC_SANITY_PROJECT_ID environment variable. Sanity will not be available.')
}

/**
 * Centralized Sanity Service for consistent client usage
 * Handles all Sanity operations with proper error handling and caching
 * Build-safe: gracefully handles missing tokens and provides fallbacks
 */
class SanityService {
  private client: SanityClient | null
  private previewClient: SanityClient | null
  private isAvailable: boolean
  
  constructor() {
    // Check if Sanity is properly configured
    this.isAvailable = !!(projectId && dataset)
    
    if (!this.isAvailable) {
      console.warn('Sanity is not properly configured. Using fallback data.')
      // Create dummy clients to prevent runtime errors
      this.client = null
      this.previewClient = null
      return
    }

    try {
      // Standard client for production content
      this.client = createClient({
        projectId,
        dataset,
        apiVersion,
        token,
        useCdn: true, // Use CDN for faster read operations
      })

      // Preview client for draft content
      this.previewClient = createClient({
        projectId,
        dataset,
        apiVersion,
        token,
        useCdn: false,
        perspective: 'previewDrafts',
      })
    } catch (error) {
      console.warn('Failed to initialize Sanity clients:', error)
      this.isAvailable = false
      this.client = null
      this.previewClient = null
    }
  }

  /**
   * Fetch data with error handling and fallback
   * Build-safe: returns null if Sanity is not available
   */
  async fetch<T>(
    query: string, 
    params: Record<string, unknown> = {}, 
    options?: { preview?: boolean; revalidate?: number }
  ): Promise<T | null> {
    // Return null immediately if Sanity is not available
    if (!this.isAvailable || !this.client) {
      console.warn('Sanity is not available, returning null for query:', query.substring(0, 50) + '...')
      return null
    }

    try {
      const client = options?.preview ? this.previewClient : this.client
      
      // Additional null check for client
      if (!client) {
        console.warn('Sanity client is not available')
        return null
      }
      
      const data = await client.fetch(query, params) as T
      return data
    } catch (error: unknown) {
      // Specific handling for authentication errors during build
      if ((error as { statusCode?: number })?.statusCode === 401 || 
          (error as { message?: string })?.message?.includes('Unauthorized')) {
        console.warn('Sanity authentication failed during build. This is expected if no token is provided.')
        return null
      }
      
      console.error('Sanity fetch error:', error)
      return null
    }
  }

  /**
   * Create or update content
   */
  async createOrUpdate(doc: Record<string, unknown> & { _id: string; _type: string }): Promise<unknown> {
    if (!this.isAvailable || !this.client) {
      throw new Error('Sanity is not available for content operations')
    }

    try {
      return await this.client.createOrReplace(doc)
    } catch (error) {
      console.error('Sanity create/update error:', error)
      throw error
    }
  }

  /**
   * Delete content
   */
  async delete(id: string): Promise<unknown> {
    if (!this.isAvailable || !this.client) {
      throw new Error('Sanity is not available for content operations')
    }

    try {
      return await this.client.delete(id)
    } catch (error) {
      console.error('Sanity delete error:', error)
      throw error
    }
  }

  /**
   * Get site settings with caching
   */
  async getSiteSettings(): Promise<SiteSettingsQueryResult> {
    return this.fetch(siteSettingsQuery, {}, { revalidate: 3600 }) // Cache for 1 hour
  }

  /**
   * Get homepage content
   */
  async getHomepage(): Promise<HomepageQueryResult> {
    return this.fetch(homepageQuery, {}, { revalidate: 1800 }) // Cache for 30 minutes
  }

  /**
   * Get services page content
   */
  async getServicesPage(): Promise<ServicesPageQueryResult> {
    return this.fetch(servicesPageQuery, {}, { revalidate: 1800 }) // Cache for 30 minutes
  }

  /**
   * Get tuning page content
   */
  async getTuningPage(): Promise<TuningPageQueryResult> {
    return this.fetch(tuningPageQuery, {}, { revalidate: 1800 }) // Cache for 30 minutes
  }

  /**
   * Get detailing page content
   */
  async getDetailingPage(): Promise<DetailingPageQueryResult | null> {
    return this.fetch(detailingPageQuery, {}, { revalidate: 1800 }) // Cache for 30 minutes
  }

  /**
   * Get legal page content
   */
  async getLegalPage(slug: string): Promise<{ title: string | null; content: unknown; lastUpdated: string | null } | null> {
    const query = groq`
      *[_type == "legalPage" && slug.current == $slug][0]{
        title,
        content,
        lastUpdated
      }
    `
    
    return this.fetch(query, { slug }, { revalidate: 7200 }) // Cache for 2 hours
  }
}

// Export singleton instance
export const sanityService = new SanityService()
export type { SanityService } 