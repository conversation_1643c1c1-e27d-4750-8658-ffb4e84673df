[{"name": "vehicle", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "vehicle"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "brand": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "model": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "year": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "engine": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "vehicleType": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "car"}, {"type": "string", "value": "motorcycle"}, {"type": "string", "value": "truck"}, {"type": "string", "value": "van"}]}, "optional": true}, "originalPower": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "originalTorque": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "stages": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"name": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "tunedPower": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "tunedTorque": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "price": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "features": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "stage"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "image": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "isActive": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "sortOrder": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}, {"name": "tuningPage", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "tuningPage"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "pageTitle": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "pageSubtitle": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "configuratorSettings": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"loadingText": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "introText": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "defaultVehicleType": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "pricePrefix": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "priceSuffix": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "priceIncludesTax": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "minSpecsDisclaimer": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}, "optional": true}, "labels": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"vehicleTypeLabel": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "brandLabel": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "modelLabel": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "yearLabel": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "motorLabel": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "selectPlaceholder": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "performanceTitle": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "originalLabel": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "tunedLabel": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "differenceLabel": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}, "optional": true}}}, {"name": "homepage", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "homepage"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "heroTitle": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "heroSubtitle": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "heroDescription": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "ctaButtonText": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "ctaSecondaryText": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "features": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "icon": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "lightning"}, {"type": "string", "value": "check"}, {"type": "string", "value": "clock"}, {"type": "string", "value": "chart"}, {"type": "string", "value": "cog"}, {"type": "string", "value": "shield"}]}, "optional": true}, "order": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "featureCard"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}}}, {"name": "servicesPage", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "servicesPage"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "heroTitle": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "heroSubtitle": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "heroDescription": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "services": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "features": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}, "buttonText": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "buttonLink": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "isExternalLink": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "color": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "blue"}, {"type": "string", "value": "green"}, {"type": "string", "value": "orange"}, {"type": "string", "value": "purple"}, {"type": "string", "value": "red"}, {"type": "string", "value": "teal"}]}, "optional": true}, "icon": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "lightning"}, {"type": "string", "value": "chart"}, {"type": "string", "value": "cog"}, {"type": "string", "value": "clock"}, {"type": "string", "value": "beaker"}, {"type": "string", "value": "chat"}]}, "optional": true}, "order": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "serviceCard"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "ctaTitle": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "ctaDescription": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "ctaButtonText": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}, {"name": "legalPage", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "legalPage"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "slug": {"type": "objectAttribute", "value": {"type": "inline", "name": "slug"}, "optional": true}, "content": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"children": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"marks": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}, "text": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "span"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "style": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "normal"}, {"type": "string", "value": "h1"}, {"type": "string", "value": "h2"}, {"type": "string", "value": "h3"}, {"type": "string", "value": "h4"}, {"type": "string", "value": "h5"}, {"type": "string", "value": "h6"}, {"type": "string", "value": "blockquote"}]}, "optional": true}, "listItem": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "bullet"}, {"type": "string", "value": "number"}]}, "optional": true}, "markDefs": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"href": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "link"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "level": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "block"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "lastUpdated": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}, {"name": "service", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "service"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "slug": {"type": "objectAttribute", "value": {"type": "inline", "name": "slug"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "content": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "union", "of": [{"type": "object", "attributes": {"children": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"marks": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}, "text": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "span"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "style": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "normal"}, {"type": "string", "value": "h1"}, {"type": "string", "value": "h2"}, {"type": "string", "value": "h3"}, {"type": "string", "value": "h4"}, {"type": "string", "value": "h5"}, {"type": "string", "value": "h6"}, {"type": "string", "value": "blockquote"}]}, "optional": true}, "listItem": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "bullet"}, {"type": "string", "value": "number"}]}, "optional": true}, "markDefs": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"href": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "link"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "level": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "block"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}, {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "alt": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}]}}, "optional": true}, "image": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "alt": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "category": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "repair"}, {"type": "string", "value": "detailing"}, {"type": "string", "value": "tuning"}]}, "optional": true}, "order": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}, {"name": "siteSettings", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "siteSettings"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "siteName": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "siteDescription": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "logo": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "navigation": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"homeLabel": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "servicesLabel": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "tuningLabel": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "contactLabel": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}, "optional": true}, "footer": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"companyName": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "address": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "rightsText": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "legalSectionTitle": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "contactSectionTitle": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "phone": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "email": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "legalLinks": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"imprintText": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "privacyText": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "termsText": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}, "optional": true}}}, "optional": true}, "swipingServices": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"text": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "order": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "swipingService"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "detailedServices": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "services": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "serviceItem"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "order": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "serviceSection"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}}}, {"name": "sanity.imagePaletteSwatch", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imagePaletteSwatch"}}, "background": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "foreground": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "population": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}}, {"name": "sanity.imagePalette", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imagePalette"}}, "darkMuted": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "lightVibrant": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "darkVibrant": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "vibrant": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "dominant": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "lightMuted": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "muted": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}}}}, {"name": "sanity.imageDimensions", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imageDimensions"}}, "height": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "width": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "aspectRatio": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}}, {"name": "sanity.imageHotspot", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imageHotspot"}}, "x": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "y": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "height": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "width": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}}, {"name": "sanity.imageCrop", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imageCrop"}}, "top": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "bottom": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "left": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "right": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}}, {"name": "sanity.fileAsset", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.fileAsset"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "originalFilename": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "label": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "altText": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "sha1hash": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "extension": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "mimeType": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "size": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "assetId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "uploadId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "path": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "url": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "source": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.assetSourceData"}, "optional": true}}}, {"name": "sanity.imageAsset", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imageAsset"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "originalFilename": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "label": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "altText": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "sha1hash": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "extension": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "mimeType": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "size": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "assetId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "uploadId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "path": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "url": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "metadata": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageMetadata"}, "optional": true}, "source": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.assetSourceData"}, "optional": true}}}, {"name": "sanity.imageMetadata", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imageMetadata"}}, "location": {"type": "objectAttribute", "value": {"type": "inline", "name": "geopoint"}, "optional": true}, "dimensions": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageDimensions"}, "optional": true}, "palette": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePalette"}, "optional": true}, "lqip": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "blurHash": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "hasAlpha": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "isOpaque": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}}}, {"name": "geopoint", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "geopoint"}}, "lat": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "lng": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "alt": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}}, {"name": "slug", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "slug"}}, "current": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "source": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}}, {"name": "sanity.assetSourceData", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.assetSourceData"}}, "name": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "id": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "url": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}}]