// Manual type definitions for Sanity schemas
// These will be replaced by auto-generated types later

export interface SiteSettings {
  _id: string
  _type: 'siteSettings'
  siteName: string
  siteDescription?: string
  logo?: {
    asset?: {
      _ref: string
      _type: 'reference'
    }
    alt?: string
  }
  navigation: {
    homeLabel: string
    servicesLabel: string
    tuningLabel: string
    contactLabel: string
  }
  footer: {
    companyName: string
    address: string
    rightsText: string
    legalSectionTitle: string
    contactSectionTitle: string
    phone: string
    email: string
    legalLinks: {
      imprintText: string
      privacyText: string
      termsText: string
    }
  }
}

export interface Homepage {
  _id: string
  _type: 'homepage'
  heroTitle: string
  heroSubtitle: string
  heroDescription: string
  ctaButtonText: string
  ctaSecondaryText: string
  features: Array<{
    title: string
    description: string
    icon: string
    order: number
  }>
  swipingServices?: Array<{
    text: string
    order: number
  }>
}

export interface ServicesPage {
  _id: string
  _type: 'servicesPage'
  heroTitle: string
  heroSubtitle: string
  heroDescription: string
  services: Array<{
    title: string
    description: string
    features: string[]
    buttonText: string
    buttonLink: string
    isExternalLink: boolean
    color: string
    icon: string
    order: number
  }>
  ctaTitle: string
  ctaDescription: string
  ctaButtonText: string
}

export interface TuningPage {
  _id: string
  _type: 'tuningPage'
  pageTitle: string
  pageSubtitle?: string
  configuratorSettings: {
    loadingText: string
    introText?: string
    defaultVehicleType: string
    pricePrefix: string
    priceSuffix: string
    priceIncludesTax: string
    minSpecsDisclaimer: string
  }
  labels: {
    vehicleTypeLabel: string
    brandLabel: string
    modelLabel: string
    yearLabel: string
    motorLabel: string
    selectPlaceholder: string
    performanceTitle: string
    originalLabel: string
    tunedLabel: string
    differenceLabel: string
  }
}

export interface LegalPage {
  _id: string
  _type: 'legalPage'
  title: string
  slug: {
    current: string
  }
  content: Array<{
    _key?: string
    _type: string
    children?: Array<{
      _key?: string
      _type: string
      marks?: string[]
      text?: string
    }>
    markDefs?: Array<{
      _key?: string
      _type: string
      href?: string
    }>
    style?: string
  }>
  lastUpdated?: string
}

// Union type for all document types
export type SanityDocument = 
  | SiteSettings 
  | Homepage 
  | ServicesPage 
  | TuningPage 
  | LegalPage

// Query result types
export type SiteSettingsQuery = SiteSettings | null
export type HomepageQuery = Homepage | null
export type ServicesPageQuery = ServicesPage | null
export type TuningPageQuery = TuningPage | null
export type LegalPageQuery = LegalPage | null 