/**
 * ---------------------------------------------------------------------------------
 * This file has been generated by Sanity TypeGen.
 * Command: `sanity typegen generate`
 *
 * Any modifications made directly to this file will be overwritten the next time
 * the TypeScript definitions are generated. Please make changes to the Sanity
 * schema definitions and/or GROQ queries if you need to update these types.
 *
 * For more information on how to use Sanity TypeGen, visit the official documentation:
 * https://www.sanity.io/docs/sanity-typegen
 * ---------------------------------------------------------------------------------
 */

// Source: schema.json
export type Vehicle = {
  _id: string
  _type: 'vehicle'
  _createdAt: string
  _updatedAt: string
  _rev: string
  brand?: string
  model?: string
  year?: string
  engine?: string
  vehicleType?: 'car' | 'motorcycle' | 'truck' | 'van'
  originalPower?: number
  originalTorque?: number
  stages?: Array<{
    name?: string
    tunedPower?: number
    tunedTorque?: number
    price?: number
    description?: string
    features?: Array<string>
    _type: 'stage'
    _key: string
  }>
  image?: {
    asset?: {
      _ref: string
      _type: 'reference'
      _weak?: boolean
      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset'
    }
    media?: unknown
    hotspot?: SanityImageHotspot
    crop?: SanityImageCrop
    _type: 'image'
  }
  isActive?: boolean
  sortOrder?: number
}

export type TuningPage = {
  _id: string
  _type: 'tuningPage'
  _createdAt: string
  _updatedAt: string
  _rev: string
  pageTitle?: string
  pageSubtitle?: string
  configuratorSettings?: {
    loadingText?: string
    introText?: string
    defaultVehicleType?: string
    pricePrefix?: string
    priceSuffix?: string
    priceIncludesTax?: string
    minSpecsDisclaimer?: string
  }
  labels?: {
    vehicleTypeLabel?: string
    brandLabel?: string
    modelLabel?: string
    yearLabel?: string
    motorLabel?: string
    selectPlaceholder?: string
    performanceTitle?: string
    originalLabel?: string
    tunedLabel?: string
    differenceLabel?: string
  }
}

export type Homepage = {
  _id: string
  _type: 'homepage'
  _createdAt: string
  _updatedAt: string
  _rev: string
  heroTitle?: string
  heroSubtitle?: string
  heroDescription?: string
  ctaButtonText?: string
  ctaSecondaryText?: string
  features?: Array<{
    title?: string
    description?: string
    icon?: 'lightning' | 'check' | 'clock' | 'chart' | 'cog' | 'shield'
    order?: number
    _type: 'featureCard'
    _key: string
  }>
}

export type ServicesPage = {
  _id: string
  _type: 'servicesPage'
  _createdAt: string
  _updatedAt: string
  _rev: string
  heroTitle?: string
  heroSubtitle?: string
  heroDescription?: string
  services?: Array<{
    title?: string
    description?: string
    features?: Array<string>
    buttonText?: string
    buttonLink?: string
    isExternalLink?: boolean
    color?: 'blue' | 'green' | 'orange' | 'purple' | 'red' | 'teal'
    icon?: 'lightning' | 'chart' | 'cog' | 'clock' | 'beaker' | 'chat'
    order?: number
    _type: 'serviceCard'
    _key: string
  }>
  ctaTitle?: string
  ctaDescription?: string
  ctaButtonText?: string
}

export type LegalPage = {
  _id: string
  _type: 'legalPage'
  _createdAt: string
  _updatedAt: string
  _rev: string
  title?: string
  slug?: Slug
  content?: Array<{
    children?: Array<{
      marks?: Array<string>
      text?: string
      _type: 'span'
      _key: string
    }>
    style?: 'normal' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'blockquote'
    listItem?: 'bullet' | 'number'
    markDefs?: Array<{
      href?: string
      _type: 'link'
      _key: string
    }>
    level?: number
    _type: 'block'
    _key: string
  }>
  lastUpdated?: string
}

export type Service = {
  _id: string
  _type: 'service'
  _createdAt: string
  _updatedAt: string
  _rev: string
  title?: string
  slug?: Slug
  description?: string
  content?: Array<
    | {
        children?: Array<{
          marks?: Array<string>
          text?: string
          _type: 'span'
          _key: string
        }>
        style?:
          | 'normal'
          | 'h1'
          | 'h2'
          | 'h3'
          | 'h4'
          | 'h5'
          | 'h6'
          | 'blockquote'
        listItem?: 'bullet' | 'number'
        markDefs?: Array<{
          href?: string
          _type: 'link'
          _key: string
        }>
        level?: number
        _type: 'block'
        _key: string
      }
    | {
        asset?: {
          _ref: string
          _type: 'reference'
          _weak?: boolean
          [internalGroqTypeReferenceTo]?: 'sanity.imageAsset'
        }
        media?: unknown
        hotspot?: SanityImageHotspot
        crop?: SanityImageCrop
        alt?: string
        _type: 'image'
        _key: string
      }
  >
  image?: {
    asset?: {
      _ref: string
      _type: 'reference'
      _weak?: boolean
      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset'
    }
    media?: unknown
    hotspot?: SanityImageHotspot
    crop?: SanityImageCrop
    alt?: string
    _type: 'image'
  }
  category?: 'repair' | 'detailing' | 'tuning'
  order?: number
}

export type SiteSettings = {
  _id: string
  _type: 'siteSettings'
  _createdAt: string
  _updatedAt: string
  _rev: string
  siteName?: string
  siteDescription?: string
  logo?: {
    asset?: {
      _ref: string
      _type: 'reference'
      _weak?: boolean
      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset'
    }
    media?: unknown
    hotspot?: SanityImageHotspot
    crop?: SanityImageCrop
    _type: 'image'
  }
  navigation?: {
    homeLabel?: string
    servicesLabel?: string
    tuningLabel?: string
    contactLabel?: string
  }
  footer?: {
    companyName?: string
    address?: string
    rightsText?: string
    legalSectionTitle?: string
    contactSectionTitle?: string
    phone?: string
    email?: string
    legalLinks?: {
      imprintText?: string
      privacyText?: string
      termsText?: string
    }
  }
  swipingServices?: Array<{
    text?: string
    order?: number
    _type: 'swipingService'
    _key: string
  }>
  detailedServices?: Array<{
    title?: string
    services?: Array<{
      title?: string
      description?: string
      _type: 'serviceItem'
      _key: string
    }>
    order?: number
    _type: 'serviceSection'
    _key: string
  }>
}

export type SanityImagePaletteSwatch = {
  _type: 'sanity.imagePaletteSwatch'
  background?: string
  foreground?: string
  population?: number
  title?: string
}

export type SanityImagePalette = {
  _type: 'sanity.imagePalette'
  darkMuted?: SanityImagePaletteSwatch
  lightVibrant?: SanityImagePaletteSwatch
  darkVibrant?: SanityImagePaletteSwatch
  vibrant?: SanityImagePaletteSwatch
  dominant?: SanityImagePaletteSwatch
  lightMuted?: SanityImagePaletteSwatch
  muted?: SanityImagePaletteSwatch
}

export type SanityImageDimensions = {
  _type: 'sanity.imageDimensions'
  height?: number
  width?: number
  aspectRatio?: number
}

export type SanityImageHotspot = {
  _type: 'sanity.imageHotspot'
  x?: number
  y?: number
  height?: number
  width?: number
}

export type SanityImageCrop = {
  _type: 'sanity.imageCrop'
  top?: number
  bottom?: number
  left?: number
  right?: number
}

export type SanityFileAsset = {
  _id: string
  _type: 'sanity.fileAsset'
  _createdAt: string
  _updatedAt: string
  _rev: string
  originalFilename?: string
  label?: string
  title?: string
  description?: string
  altText?: string
  sha1hash?: string
  extension?: string
  mimeType?: string
  size?: number
  assetId?: string
  uploadId?: string
  path?: string
  url?: string
  source?: SanityAssetSourceData
}

export type SanityImageAsset = {
  _id: string
  _type: 'sanity.imageAsset'
  _createdAt: string
  _updatedAt: string
  _rev: string
  originalFilename?: string
  label?: string
  title?: string
  description?: string
  altText?: string
  sha1hash?: string
  extension?: string
  mimeType?: string
  size?: number
  assetId?: string
  uploadId?: string
  path?: string
  url?: string
  metadata?: SanityImageMetadata
  source?: SanityAssetSourceData
}

export type SanityImageMetadata = {
  _type: 'sanity.imageMetadata'
  location?: Geopoint
  dimensions?: SanityImageDimensions
  palette?: SanityImagePalette
  lqip?: string
  blurHash?: string
  hasAlpha?: boolean
  isOpaque?: boolean
}

export type Geopoint = {
  _type: 'geopoint'
  lat?: number
  lng?: number
  alt?: number
}

export type Slug = {
  _type: 'slug'
  current?: string
  source?: string
}

export type SanityAssetSourceData = {
  _type: 'sanity.assetSourceData'
  name?: string
  id?: string
  url?: string
}

export type AllSanitySchemaTypes =
  | Vehicle
  | TuningPage
  | Homepage
  | ServicesPage
  | LegalPage
  | Service
  | SiteSettings
  | SanityImagePaletteSwatch
  | SanityImagePalette
  | SanityImageDimensions
  | SanityImageHotspot
  | SanityImageCrop
  | SanityFileAsset
  | SanityImageAsset
  | SanityImageMetadata
  | Geopoint
  | Slug
  | SanityAssetSourceData
export declare const internalGroqTypeReferenceTo: unique symbol
// Source: ./src/sanity/lib/queries.ts
// Variable: servicesPageQuery
// Query: *[_type == "servicesPage"][0]{    heroTitle,    heroSubtitle,    heroDescription,    services[] | order(order asc) {      title,      description,      features,      buttonText,      buttonLink,      isExternalLink,      color,      icon,      order    },    ctaTitle,    ctaDescription,    ctaButtonText  }
export type ServicesPageQueryResult = {
  heroTitle: string | null
  heroSubtitle: string | null
  heroDescription: string | null
  services: Array<{
    title: string | null
    description: string | null
    features: Array<string> | null
    buttonText: string | null
    buttonLink: string | null
    isExternalLink: boolean | null
    color: 'blue' | 'green' | 'orange' | 'purple' | 'red' | 'teal' | null
    icon: 'beaker' | 'chart' | 'chat' | 'clock' | 'cog' | 'lightning' | null
    order: number | null
  }> | null
  ctaTitle: string | null
  ctaDescription: string | null
  ctaButtonText: string | null
} | null
// Variable: homepageQuery
// Query: *[_type == "homepage"][0]{    heroTitle,    heroSubtitle,    heroDescription,    ctaButtonText,    ctaSecondaryText,    features[] | order(order asc) {      title,      description,      icon,      order    },    swipingServices[] | order(order asc) {      text,      order    },    detailedServices[] | order(order asc) {      title,      order,      services[] {        title,        description      }    }  }
export type HomepageQueryResult = {
  heroTitle: string | null
  heroSubtitle: string | null
  heroDescription: string | null
  ctaButtonText: string | null
  ctaSecondaryText: string | null
  features: Array<{
    title: string | null
    description: string | null
    icon: 'chart' | 'check' | 'clock' | 'cog' | 'lightning' | 'shield' | null
    order: number | null
  }> | null
  swipingServices: null
  detailedServices: null
} | null
// Variable: tuningPageQuery
// Query: *[_type == "tuningPage"][0]{    pageTitle,    pageSubtitle,    configuratorSettings {      loadingText,      introText,      defaultVehicleType,      pricePrefix,      priceSuffix,      priceIncludesTax,      minSpecsDisclaimer    },    labels {      vehicleTypeLabel,      brandLabel,      modelLabel,      yearLabel,      motorLabel,      selectPlaceholder,      performanceTitle,      originalLabel,      tunedLabel,      differenceLabel    }  }
export type TuningPageQueryResult = {
  pageTitle: string | null
  pageSubtitle: string | null
  configuratorSettings: {
    loadingText: string | null
    introText: string | null
    defaultVehicleType: string | null
    pricePrefix: string | null
    priceSuffix: string | null
    priceIncludesTax: string | null
    minSpecsDisclaimer: string | null
  } | null
  labels: {
    vehicleTypeLabel: string | null
    brandLabel: string | null
    modelLabel: string | null
    yearLabel: string | null
    motorLabel: string | null
    selectPlaceholder: string | null
    performanceTitle: string | null
    originalLabel: string | null
    tunedLabel: string | null
    differenceLabel: string | null
  } | null
} | null
// Variable: siteSettingsQuery
// Query: *[_type == "siteSettings"][0]{    siteName,    siteDescription,    logo,    navigation {      homeLabel,      servicesLabel,      tuningLabel,      detailingLabel,      contactLabel    },    footer {      companyName,      address,      rightsText,      legalSectionTitle,      contactSectionTitle,      phone,      email,      whatsapp,      legalLinks {        imprintText,        privacyText,        termsText      }    }  }
export type SiteSettingsQueryResult = {
  siteName: string | null
  siteDescription: string | null
  logo: {
    asset?: {
      _ref: string
      _type: 'reference'
      _weak?: boolean
      [internalGroqTypeReferenceTo]?: 'sanity.imageAsset'
    }
    media?: unknown
    hotspot?: SanityImageHotspot
    crop?: SanityImageCrop
    _type: 'image'
  } | null
  navigation: {
    homeLabel: string | null
    servicesLabel: string | null
    tuningLabel: string | null
    detailingLabel: null
    contactLabel: string | null
  } | null
  footer: {
    companyName: string | null
    address: string | null
    rightsText: string | null
    legalSectionTitle: string | null
    contactSectionTitle: string | null
    phone: string | null
    email: string | null
    whatsapp: null
    legalLinks: {
      imprintText: string | null
      privacyText: string | null
      termsText: string | null
    } | null
  } | null
} | null
// Variable: detailingPageQuery
// Query: *[_type == "detailingPage"][0] {  _id,  title,  heroSection {    heroTitle,    heroSubtitle,    heroDescription  },  services[] {    title,    description,    features,    buttonText,    buttonLink,    isExternalLink,    color,    icon,    order,    _key  } | order(order asc),  ctaSection {    ctaTitle,    ctaDescription,    ctaButtonText  },  lastUpdated}
export type DetailingPageQueryResult = null

// Source: ./src/sanity/services/SanityService.ts
// Variable: query
// Query: *[_type == "legalPage" && slug.current == $slug][0]{        title,        content,        lastUpdated      }
export type QueryResult = {
  title: string | null
  content: Array<{
    children?: Array<{
      marks?: Array<string>
      text?: string
      _type: 'span'
      _key: string
    }>
    style?: 'blockquote' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'normal'
    listItem?: 'bullet' | 'number'
    markDefs?: Array<{
      href?: string
      _type: 'link'
      _key: string
    }>
    level?: number
    _type: 'block'
    _key: string
  }> | null
  lastUpdated: string | null
} | null

// Query TypeMap
import '@sanity/client'
declare module '@sanity/client' {
  interface SanityQueries {
    '\n  *[_type == "servicesPage"][0]{\n    heroTitle,\n    heroSubtitle,\n    heroDescription,\n    services[] | order(order asc) {\n      title,\n      description,\n      features,\n      buttonText,\n      buttonLink,\n      isExternalLink,\n      color,\n      icon,\n      order\n    },\n    ctaTitle,\n    ctaDescription,\n    ctaButtonText\n  }\n': ServicesPageQueryResult
    '\n  *[_type == "homepage"][0]{\n    heroTitle,\n    heroSubtitle,\n    heroDescription,\n    ctaButtonText,\n    ctaSecondaryText,\n    features[] | order(order asc) {\n      title,\n      description,\n      icon,\n      order\n    },\n    swipingServices[] | order(order asc) {\n      text,\n      order\n    },\n    detailedServices[] | order(order asc) {\n      title,\n      order,\n      services[] {\n        title,\n        description\n      }\n    }\n  }\n': HomepageQueryResult
    '\n  *[_type == "tuningPage"][0]{\n    pageTitle,\n    pageSubtitle,\n    configuratorSettings {\n      loadingText,\n      introText,\n      defaultVehicleType,\n      pricePrefix,\n      priceSuffix,\n      priceIncludesTax,\n      minSpecsDisclaimer\n    },\n    labels {\n      vehicleTypeLabel,\n      brandLabel,\n      modelLabel,\n      yearLabel,\n      motorLabel,\n      selectPlaceholder,\n      performanceTitle,\n      originalLabel,\n      tunedLabel,\n      differenceLabel\n    }\n  }\n': TuningPageQueryResult
    '\n  *[_type == "siteSettings"][0]{\n    siteName,\n    siteDescription,\n    logo,\n    navigation {\n      homeLabel,\n      servicesLabel,\n      tuningLabel,\n      detailingLabel,\n      contactLabel\n    },\n    footer {\n      companyName,\n      address,\n      rightsText,\n      legalSectionTitle,\n      contactSectionTitle,\n      phone,\n      email,\n      whatsapp,\n      legalLinks {\n        imprintText,\n        privacyText,\n        termsText\n      }\n    }\n  }\n': SiteSettingsQueryResult
    '*[_type == "detailingPage"][0] {\n  _id,\n  title,\n  heroSection {\n    heroTitle,\n    heroSubtitle,\n    heroDescription\n  },\n  services[] {\n    title,\n    description,\n    features,\n    buttonText,\n    buttonLink,\n    isExternalLink,\n    color,\n    icon,\n    order,\n    _key\n  } | order(order asc),\n  ctaSection {\n    ctaTitle,\n    ctaDescription,\n    ctaButtonText\n  },\n  lastUpdated\n}': DetailingPageQueryResult
    '\n      *[_type == "legalPage" && slug.current == $slug][0]{\n        title,\n        content,\n        lastUpdated\n      }\n    ': QueryResult
  }
}
