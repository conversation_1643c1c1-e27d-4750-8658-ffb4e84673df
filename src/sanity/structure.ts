import type {StructureResolver} from 'sanity/structure'

// https://www.sanity.io/docs/structure-builder-cheat-sheet
const excludedDocumentTypes = [
  'media.tag',
  'siteSettings',
  'homepage',
  'tuningPage',
  'detailingPage',
  'servicesPage',
  'legalPage',
]

export const structure: StructureResolver = (S) =>
  S.list()
    .title('Content')
    .items([
      S.listItem()
        .title('Site Settings')
        .id('siteSettings')
        .child(
          S.document()
            .schemaType('siteSettings')
            .documentId('siteSettings'),
        ),
      S.divider(),
      S.listItem()
        .title('Fahrzeuge')
        .schemaType('vehicle')
        .child(S.documentTypeList('vehicle').title('Fahrzeuge')),
      S.divider(),
      S.listItem()
        .title('Seiten')
        .id('pages')
        .child(
          S.list()
            .title('Seiten')
            .items([
              S.listItem()
                .title('Homepage')
                .child(
                  S.document()
                    .schemaType('homepage')
                    .documentId('homepage'),
                ),
              S.listItem()
                .title('Tuning-Seite')
                .child(
                  S.document()
                    .schemaType('tuningPage')
                    .documentId('tuning-page'),
                ),
              S.listItem()
                .title('Aufbereitungs-Seite')
                .child(
                  S.document()
                    .schemaType('detailingPage')
                    .documentId('detailing-page'),
                ),
                 S.listItem()
                .title('Services-Seite')
                .child(
                  S.document()
                    .schemaType('servicesPage')
                    .documentId('services-page'),
                ),
              S.listItem()
                .title('Rechtliche Seiten (AGB, etc.)')
                .schemaType('legalPage')
                .child(S.documentTypeList('legalPage').title('Rechtliche Seiten')),
            ]),
        ),
      S.divider(),
      // Filter out excluded types from the default list
      ...S.documentTypeListItems().filter(
        listItem => !excludedDocumentTypes.includes(listItem.getId() as string),
      ),
    ])
