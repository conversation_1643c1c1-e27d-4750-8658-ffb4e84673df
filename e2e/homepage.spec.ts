import { test, expect } from '@playwright/test'

test.describe('Homepage', () => {
  test('should load and display main content', async ({ page }) => {
    await page.goto('/')

    // Check if page loads successfully
    await expect(page).toHaveTitle(/DB-Performance/)

    // Check hero section
    await expect(page.locator('h1')).toContainText('DB-Performance')
    
    // Check navigation elements
    await expect(page.locator('nav')).toBeVisible()
    await expect(page.getByRole('link', { name: 'Start' })).toBeVisible()
    await expect(page.getByRole('link', { name: 'Dienstleistungen' })).toBeVisible()
    await expect(page.getByRole('link', { name: 'Tuning-Konfigurator' })).toBeVisible()
    await expect(page.getByRole('link', { name: 'Kontakt' })).toBeVisible()

    // Check CTA buttons
    await expect(page.getByRole('link', { name: 'Tuning-Konfigurator starten' })).toBeVisible()
    await expect(page.getByRole('link', { name: '<PERSON>hr erfahren' })).toBeVisible()

    // Check features section
    await expect(page.locator('text=Leistungssteigerung')).toBeVisible()
    await expect(page.locator('text=Qualitätsgarantie')).toBeVisible()
    await expect(page.locator('text=Schneller Service')).toBeVisible()

    // Check footer
    await expect(page.locator('footer')).toBeVisible()
    await expect(page.locator('footer')).toContainText('DB-Performance')
  })

  test('should navigate to tuning page when CTA is clicked', async ({ page }) => {
    await page.goto('/')

    // Click on the main CTA button
    await page.getByRole('link', { name: 'Tuning-Konfigurator starten' }).click()

    // Should navigate to tuning page
    await expect(page).toHaveURL('/tuning')
  })

  test('should navigate to services page when secondary CTA is clicked', async ({ page }) => {
    await page.goto('/')

    // Click on the secondary CTA button
    await page.getByRole('link', { name: 'Mehr erfahren' }).click()

    // Should navigate to services page
    await expect(page).toHaveURL('/services')
  })

  test('should have proper accessibility', async ({ page }) => {
    await page.goto('/')

    // Check for basic accessibility requirements
    const heading = page.locator('h1')
    await expect(heading).toBeVisible()

    // Check that all links have accessible names
    const links = page.locator('a')
    const linkCount = await links.count()
    
    for (let i = 0; i < linkCount; i++) {
      const link = links.nth(i)
      const text = await link.textContent()
      const ariaLabel = await link.getAttribute('aria-label')
      
      // Each link should have either visible text or aria-label
      expect(text || ariaLabel).toBeTruthy()
    }

    // Check color contrast (basic check)
    await expect(page.locator('body')).toHaveCSS('color', /rgb\(\d+,\s*\d+,\s*\d+\)/)
  })

  test('should be responsive on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    await page.goto('/')

    // Check that content is still visible and usable
    await expect(page.locator('h1')).toBeVisible()
    await expect(page.getByRole('link', { name: 'Tuning-Konfigurator starten' })).toBeVisible()
    
    // Navigation might be hidden on mobile, but logo should be visible
    await expect(page.locator('nav')).toBeVisible()
  })
}) 