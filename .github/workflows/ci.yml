name: CI

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18.x, 20.x]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8

      - name: Setup Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run linting
        run: pnpm lint

      - name: Run type checking
        run: pnpm build --dry-run || pnpm tsc --noEmit

      - name: Run unit tests
        run: pnpm test --coverage --watchAll=false

      - name: Check coverage threshold
        run: |
          # Fail if coverage is below 90%
          pnpm test:coverage --coverageThreshold='{"global":{"branches":90,"functions":90,"lines":90,"statements":90}}' --passWithNoTests

      - name: Setup Playwright
        run: pnpm playwright install chromium webkit

      - name: Run E2E tests
        run: pnpm test:e2e
        env:
          CI: true

      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: test-results-${{ matrix.node-version }}
          path: |
            test-results/
            playwright-report/
            coverage/

  build:
    runs-on: ubuntu-latest
    needs: test

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'pnpm'

      - name: Install Vercel CLI
        run: pnpm add -g vercel@latest

      - name: Pull Vercel Environment Information
        run: vercel pull --yes --environment=production --token=${{ secrets.VERCEL_TOKEN }}

      - name: Build Project Artifacts
        run: vercel build --prod --token=${{ secrets.VERCEL_TOKEN }}

      - name: Security audit
        run: pnpm audit --prod --audit-level=high

  renovate:
    runs-on: ubuntu-latest
    if: github.event_name == 'schedule' || github.event_name == 'workflow_dispatch'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'

      - name: Run Renovate
        uses: renovatebot/github-action@v39.2.0
        with:
          configurationFile: renovate.json
          token: ${{ secrets.RENOVATE_TOKEN }}

  quality-gate:
    runs-on: ubuntu-latest
    needs: [test, build]
    if: always()

    steps:
      - name: Check test results
        run: |
          if [[ "${{ needs.test.result }}" != "success" ]]; then
            echo "❌ Tests failed"
            exit 1
          fi
          
          if [[ "${{ needs.build.result }}" != "success" ]]; then
            echo "❌ Build failed"
            exit 1
          fi
          
          echo "✅ All quality gates passed" 