{"extends": ["config:base"], "prHourlyLimit": 1, "prConcurrentLimit": 3, "packageRules": [{"updateTypes": ["minor", "patch"], "groupName": "all-minor-patch", "automerge": true, "automergeType": "pr", "requiredStatusChecks": null}, {"updateTypes": ["major"], "groupName": "all-major", "automerge": false, "addLabels": ["dependencies", "major-update"]}, {"packagePatterns": ["^@types/"], "groupName": "type definitions", "automerge": true}, {"packageNames": ["@playwright/test", "playwright"], "groupName": "playwright", "automerge": true}, {"packageNames": ["eslint", "prettier"], "groupName": "linting", "automerge": true}], "rebaseWhen": "conflicted", "dependencyDashboard": true, "dependencyDashboardTitle": "🤖 Dependency Dashboard", "schedule": ["before 6am on monday"], "timezone": "Europe/Zurich", "commitMessagePrefix": "⬆️", "commitMessageTopic": "{{depName}}", "commitMessageExtra": "to {{newVersion}}", "semanticCommits": "enabled", "semanticCommitType": "chore", "semanticCommitScope": "deps", "assignees": ["@your-github-username"], "reviewers": ["@your-github-username"], "labels": ["dependencies"], "rangeStrategy": "bump"}