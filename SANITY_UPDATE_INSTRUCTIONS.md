# 📞 Sanity Studio - Telefonnummer im Production Dataset aktualisieren

## 🎯 Ziel
Die Telefonnummer im Production Dataset von `+41 XX XXX XX XX` (oder fehlend) zu `+41 76 250 07 61` ändern.

## 📋 Schritt-für-Schritt Anleitung

### 1. Sanity Studio öffnen
- URL: https://dp-performance-fdj3x968f-elsi06s-projects.vercel.app/studio
- Oder lokal: `npm run dev` und dann http://localhost:3000/studio

### 2. Dataset wechseln
- Oben rechts im Studio auf das Dataset-Dropdown klicken
- Von "development" zu **"production"** wechseln

### 3. Site Settings öffnen
- In der linken Sidebar auf **"Site Settings"** klicken
- Falls nicht sichtbar, nach "siteSettings" suchen

### 4. Footer-Bereich finden
- Scrollen Sie zum **"Footer"** Bereich
- Klappen Sie den Footer-Bereich auf

### 5. Telefonnummer aktualisieren
- Suchen Sie das Feld **"Phone Number"**
- Aktueller Wert: `Nicht gesetzt` oder `+41 XX XXX XX XX`
- Neuer Wert: `+41 76 250 07 61`

### 6. Speichern
- Klicken Sie auf **"Publish"** oder **"Save"**
- Bestätigen Sie die Änderung

## 🔍 Aktuelle Daten (aus unserem Scan)

### Development Dataset:
```
Company: DB-Performance
Email: <EMAIL>
WhatsApp: +41762500761
Phone: +41 XX XXX XX XX  ← Platzhalter
```

### Production Dataset:
```
Company: DB-Performance
Email: <EMAIL>
WhatsApp: +41762500761
Phone: NOT SET  ← Fehlt komplett!
```

## ✅ Nach der Aktualisierung sollte es so aussehen:
```
Company: DB-Performance
Email: <EMAIL>
WhatsApp: +41762500761
Phone: +41 76 250 07 61  ← Korrekte Nummer
```

## 🚀 Alternative: CLI-Befehl (falls Schreibrechte vorhanden)
```bash
# Dieses Skript würde funktionieren, wenn wir Schreibrechte hätten:
node scripts/update-sanity-phone-production.js
```

## 📝 Notizen
- Die WhatsApp-Nummer ist bereits korrekt gesetzt
- Die E-Mail-Adresse ist korrekt
- Nur die Telefonnummer fehlt im Production Dataset
- Development Dataset hat einen Platzhalter, der auch aktualisiert werden könnte
