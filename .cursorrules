## Cursor Rules – DB‑Performance Codebase

### 1. Language & Comments
- Code comments, commit messages, PR descriptions **English**.
- UI copy Swiss German (ss, no ß).
- Display strings live in `src/strings.ts`.

### 2. Folder Structure

.
├─ src/
│  ├─ app/              # Next.js route tree
│  ├─ components/       # Reusable UI
│  ├─ db/               # Supabase client & helpers
│  ├─ sanity/           # GROQ queries & helpers
│  ├─ strings.ts        # UI copy
│  └─ styles/           # Tailwind overrides
├─ data/batches/        # Raw JSON vehicle batches
├─ scripts/             # Seed, misc CLI
├─ supabase/            # SQL migrations
├─ test/                # Mirrors src/
├─ .github/
│  ├─ workflows/        # ci.yml, db-migrate.yml
│  └─ renovate.json
└─ vercel.json


Rules:
- Routing logic only in `src/app`.
- Data access only in `src/db`.
- Tests mirror real code paths.
- Scripts idempotent (`pnpm ts-node scripts/seed.ts`).

### 3. Testing
- Unit + jest-axe per component.
- Coverage ≥ 90 %.
- Playwright for core flows.

### 4. Automated Dependency Updates
- Renovate daily, ≤3 PRs.
- Patch/minor grouped & automerged.
- Major grouped, manual review.
- Rebase on conflict, dashboard issue enabled.

### 5. Database Migrations
- SQL in `supabase/migrations/*`.
- CI pushes on `main` merge.
- No manual Studio edits.

### 6. Security & Privacy
- Never commit secrets.
- Validate API inputs (Zod).
- Limit Supabase service key scope.

### 7. Performance
- Prefer server comps; lazy‑load heavy client comps.
- Use `next/image` with Sanity CDN params.

### 8. Implementation Principles (Developer Prompt)
- Think step‑by‑step, write pseudocode first.
- Produce DRY, complete, readable code; no TODOs.
- Tailwind‑only styling; prefer `class:` groups over ternaries.
- Descriptive names; handlers prefixed with `handle`.
- Accessibility: `tabIndex={0}`, `aria‑label`, `onKeyDown`.
- Functions as typed const arrows (`const toggle: () => void = () => {}`).
- Include all imports; consistent naming.
- If uncertain, state uncertainty rather than guess.