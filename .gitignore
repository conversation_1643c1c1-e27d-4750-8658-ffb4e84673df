# Dependencies
node_modules/
/.pnp
.pnp.js

# Production builds
/.next/
/out/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
node_modules/
jspm_packages/

# Snowpack dependency directory (https://snowpack.dev/)
web_modules/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Environment files - CRITICAL FOR SECURITY
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local
.env.backup
.env.local.backup

# Vercel
.vercel

# Supabase
supabase/.temp/
supabase/.branches/
supabase/.temp
**/supabase/.temp/**

# Sanity
.sanity/

# MacOS
.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Playwright Test Results
test-results/
playwright-report/
playwright/.cache/

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Cursor
.cursor/

# Husky
.husky/_/ 